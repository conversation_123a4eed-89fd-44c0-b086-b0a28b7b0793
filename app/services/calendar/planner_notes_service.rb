# frozen_string_literal: true

module Calendar
  class PlannerNotesService
    def initialize(user_id, options = {})
      @user_id = user_id
      @options = options
    end

    def call
      canvas_sync_client.get(api_path, query_params).all_pages!.to_a
    end

    private

    attr_reader :user_id, :options

    def api_path
      'api/v1/planner_notes'
    end

    def query_params
      params = {
        # Call API on behalf of Specific User
        as_user_id: user_id,

        # Context codes for filtering by course
        context_codes: options[:context_codes]
      }

      # Only add date range if not requesting undated items
      unless undated?
        params[:start_date] = options[:start_date] || default_start_date
        params[:end_date] = options[:end_date] || default_end_date
      end

      # Additional filters
      params[:undated] = 1 if undated?

      params
    end

    def undated?
      # Check for various truthy representations of undated
      [true, 'true', 1, '1'].include?(options[:undated])
    end

    def default_start_date
      Date.today.beginning_of_month.iso8601
    end

    def default_end_date
      Date.today.end_of_month.iso8601
    end
  end
end
