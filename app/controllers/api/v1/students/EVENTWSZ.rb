context_code
course_id
date
description
end_date
html_url
id
start_date
title
todo_date
user_id
workflow_state

Event Type ICON [Event/Assignment/Quiz/Discussion]
Event/Assignment/Quiz/Discussion


NOTE
 {"id"=>2,
 "todo_date"=>"2025-09-05T18:29:00Z",
 "title"=>"TEST TODO Undated",
 "details"=>"",
 "user_id"=>4,
 "course_id"=>2,
 "workflow_state"=>"active",
 "created_at"=>"2025-09-04T07:18:42Z",
 "updated_at"=>"2025-09-04T07:18:42Z"}

ITEM
 {"context_type"=>"User",
 "user_id"=>4,
 "plannable_id"=>3,
 "planner_override"=>nil,
 "plannable_type"=>"calendar_event",
 "new_activity"=>false,
 "submissions"=>false,
 "plannable_date"=>"2025-08-12T18:30:00Z",
 "plannable"=>
  {"id"=>3,
   "title"=>"Student 1 Event 1",
   "location_name"=>"",
   "created_at"=>"2025-08-20T05:31:26Z",
   "updated_at"=>"2025-08-20T05:31:26Z",
   "all_day"=>true,
   "location_address"=>nil,
   "description"=>nil,
   "start_at"=>"2025-08-12T18:30:00Z",
   "end_at"=>"2025-08-12T18:30:00Z"},
 "html_url"=>"http://canvas.docker/calendar?event_id=3&include_contexts=user_4",
 "context_name"=>"Student 1"}
=======
{"context_type"=>"Course",
 "course_id"=>1,
 "plannable_id"=>11,
 "planner_override"=>nil,
 "plannable_type"=>"assignment",
 "new_activity"=>false,
 "submissions"=>{"submitted"=>true, "excused"=>false, "graded"=>false, "posted_at"=>nil, "late"=>true, "missing"=>false, "needs_grading"=>true, "has_feedback"=>false, "redo_request"=>false},
 "plannable_date"=>"2025-09-03T05:59:00Z",
 "plannable"=>{"id"=>11, "title"=>"assignment1 Ungraded", "created_at"=>"2025-09-04T07:09:42Z", "updated_at"=>"2025-09-04T07:09:58Z", "points_possible"=>10.0, "due_at"=>"2025-09-03T05:59:00Z"},
 "html_url"=>"/courses/1/assignments/11/submissions/4",
 "context_name"=>"Shard1 Course",
 "context_image"=>nil}
====
{"context_type"=>"Course",
 "course_id"=>1,
 "plannable_id"=>3,
 "planner_override"=>nil,
 "plannable_type"=>"announcement",
 "new_activity"=>true,
 "submissions"=>false,
 "plannable_date"=>"2025-09-04T07:12:14Z",
 "plannable"=>{"id"=>3, "title"=>"Shard 1 Announcement", "unread_count"=>0, "read_state"=>"unread", "created_at"=>"2025-09-04T07:12:14Z", "updated_at"=>"2025-09-04T07:12:31Z"},
 "html_url"=>"/courses/1/discussion_topics/3",
 "context_name"=>"Shard1 Course",
 "context_image"=>nil}
=====
{"plannable_id"=>1,
 "planner_override"=>nil,
 "plannable_type"=>"planner_note",
 "new_activity"=>false,
 "submissions"=>false,
 "plannable_date"=>"2025-09-04T18:29:00Z",
 "plannable"=>{"id"=>1, "title"=>"TEST TODO", "course_id"=>nil, "todo_date"=>"2025-09-04T18:29:00Z", "details"=>"", "created_at"=>"2025-09-04T07:18:26Z", "updated_at"=>"2025-09-04T07:18:26Z", "user_id"=>4}}
=====
{"context_type"=>"Course",
 "course_id"=>2,
 "plannable_id"=>1,
 "planner_override"=>nil,
 "plannable_type"=>"discussion_topic",
 "new_activity"=>true,
 "submissions"=>{"submitted"=>false, "excused"=>false, "graded"=>false, "posted_at"=>nil, "late"=>false, "missing"=>false, "needs_grading"=>false, "has_feedback"=>false, "redo_request"=>false},
 "plannable_date"=>"2025-09-05T05:59:59Z",
 "plannable"=>
  {"id"=>1,
   "title"=>"discussion1",
   "todo_date"=>nil,
   "unread_count"=>0,
   "read_state"=>"unread",
   "created_at"=>"2025-08-18T12:32:44Z",
   "updated_at"=>"2025-09-03T04:53:49Z",
   "assignment_id"=>2,
   "points_possible"=>10.0,
   "due_at"=>"2025-09-05T05:59:59Z"},
 "html_url"=>"/courses/2/discussion_topics/1",
 "context_name"=>"Course 1",
 "context_image"=>nil}
======
{"context_type"=>"Course",
 "course_id"=>2,
 "plannable_id"=>1,
 "planner_override"=>nil,
 "plannable_type"=>"quiz",
 "new_activity"=>false,
 "submissions"=>{"submitted"=>false, "excused"=>false, "graded"=>false, "posted_at"=>nil, "late"=>false, "missing"=>false, "needs_grading"=>false, "has_feedback"=>false, "redo_request"=>false},
 "plannable_date"=>"2025-09-20T05:59:59Z",
 "plannable"=>{"id"=>1, "title"=>"GradedQuiz1", "created_at"=>"2025-08-18T12:32:44Z", "updated_at"=>"2025-09-03T04:54:14Z", "assignment_id"=>1, "points_possible"=>58.0, "due_at"=>"2025-09-20T05:59:59Z"},
 "html_url"=>"/courses/2/quizzes/1",
 "context_name"=>"Course 1",
 "context_image"=>nil}

Event
 {"id"=>5,
 "title"=>"TEST 1",
 "start_at"=>"2025-09-03T19:15:00Z",
 "end_at"=>"2025-09-03T22:30:00Z",
 "workflow_state"=>"active",
 "created_at"=>"2025-09-03T04:52:52Z",
 "updated_at"=>"2025-09-04T07:08:06Z",
 "all_day"=>false,
 "all_day_date"=>"2025-09-04",
 "comments"=>nil,
 "rrule"=>"",
 "series_uuid"=>nil,
 "blackout_date"=>false,
 "location_address"=>nil,
 "location_name"=>"",
 "type"=>"event",
 "description"=>nil,
 "child_events_count"=>0,
 "all_context_codes"=>"user_4",
 "context_code"=>"user_4",
 "context_name"=>"Student 1",
 "context_color"=>nil,
 "parent_event_id"=>nil,
 "hidden"=>false,
 "child_events"=>[],
 "url"=>"http://canvas.docker/api/v1/calendar_events/5",
 "html_url"=>"http://canvas.docker/calendar?event_id=5&include_contexts=user_4",
 "duplicates"=>[],
 "important_dates"=>false}
