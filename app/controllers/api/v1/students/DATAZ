EVENT
context_code
context_name
title
description
workflow_state
start_at
end_at
location_name
location_address
type -> event
html_url
color_key = context_code

ITEM
context_type
course_id
context_name
context_code -> context_type == 'course' ? 'course_' + course_id : 'user_' + student.id
date -> plannable_date or plannable -> due_at
html_url
id -> plannable_id
title -> plannable -> title
description -> plannable -> description / details
user_id -> plannable -> user_id
location_address -> plannable -> location_address
start_at -> plannable -> start_at
end_at -> plannable -> end_at
todo_date -> plannable -> due_at
type -> plannable_type
color_key = context_code -> context_type == Course ? "course_" + course_id : 'user_' + user_id

NOTE
id
user_id
course_id
date -> todo_date
title
description
workflow_state
context_code -> course_id ? "course_" + course_id : "user_" + user_id
type -> 'planner_note'
color_key = context_code -> course_id ? "course_" + course_id : "user_" + user_id
