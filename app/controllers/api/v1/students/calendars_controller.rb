# frozen_string_literal: true

class Api::V1::Students::CalendarsController < ApplicationController
  before_action :set_student

  def all_events
    authorize! :read, User

    normalized_events = []

    # Call EventsService 3 times with different types
    [nil, 'assignment', 'sub_assignment'].each do |event_type|
      service_params = calendar_params.merge(type: event_type)
      service = Calendar::EventsService.new(@student.canvas_id, service_params)
      events = service.call

      events.each do |event|
        title = event['title'] || event[:title]
        context_code = event['context_code'] || event[:context_code]
        context_id = context_code ? context_code.to_s.split('_').last&.to_i : nil

        normalized_events << {
          name: title,
          description: event['description'] || event[:description],
          title: title,
          location: event['location_name'] || event[:location_name],
          context_id: context_id,
          context_code: context_code,
          start_date: event['start_at'] || event[:start_at],
          end_date: event['end_at'] || event[:end_at],
          plannable_type: 'calendar_event',
          plannable_date: event['start_at'] || event[:start_at],
          context: event['context_name'] || event[:context_name],
          todo_date: nil
        }
      end
    end

    # Call PlannerNotesService
    planner_service = Calendar::PlannerNotesService.new(@student.canvas_id, calendar_params)
    planner_notes = planner_service.call

    planner_notes.each do |note|
      title = note['title'] || note[:title]
      todo_date = note['todo_date'] || note[:todo_date]
      course_id = note['course_id'] || note[:course_id]

      normalized_events << {
        name: title,
        description: note['details'] || note[:details],
        title: title,
        location: nil,
        context_id: course_id,
        context_code: course_id ? "course_#{course_id}" : nil,
        start_date: todo_date,
        end_date: todo_date,
        plannable_type: 'planner_note',
        plannable_date: todo_date,
        context: nil,
        todo_date: todo_date
      }
    end

    # Call ItemsService
    items_service = Calendar::ItemsService.new(@student.canvas_id, calendar_params)
    items = items_service.call

    items.each do |item|
      binding.pry if item[:plannable_type] != 'calendar_event'

      plannable = item['plannable'] || item[:plannable] || {}
      course_id = item['course_id'] || item[:course_id]
      user_id = item['user_id'] || item[:user_id]
      context_code = course_id ? "course_#{course_id}" : (user_id ? "user_#{user_id}" : nil)

      normalized_events << {
        name: plannable['title'] || plannable[:title],
        description: plannable['description'] || plannable[:description],
        title: plannable['title'] || plannable[:title],
        location: plannable['location_name'] || plannable[:location_name],
        context_id: course_id || user_id,
        context_code: context_code,
        start_date: plannable['start_at'] || plannable[:start_at] || plannable['due_at'] || plannable[:due_at],
        end_date: plannable['end_at'] || plannable[:end_at] || plannable['due_at'] || plannable[:due_at],
        plannable_type: item['plannable_type'] || item[:plannable_type],
        plannable_date: item['plannable_date'] || item[:plannable_date],
        context: item['context_name'] || item[:context_name],
        todo_date: plannable['todo_date'] || plannable[:todo_date]
      }
    end

    @all_events = normalized_events
  end

  # TODO: May not be used
  def events
    authorize! :read, User

    service = Calendar::EventsService.new(@student.canvas_id, calendar_params)
    @events = service.call
  end

  # TODO: May not be used
  def planner_notes
    authorize! :read, User

    service = Calendar::PlannerNotesService.new(@student.canvas_id, calendar_params)
    @planner_notes = service.call
  end

  # TODO: May not be used
  def items
    authorize! :read, User

    service = Calendar::ItemsService.new(@student.canvas_id, calendar_params)
    @items = service.call
  end

  private

  def set_student
    @student = find_student_by_canvas_id(params[:student_id])

    return if @student

    render json: { error: 'Student not found' }, status: :not_found
    nil
  end

  def find_student_by_canvas_id(canvas_id)
    launch_context_id = current_ability.launch_context.canvas_id
    base_query = if current_ability.launch_context.is_a?(Account)
                   User.active_students_for_account(launch_context_id)
                 elsif current_ability.launch_context.is_a?(Course)
                   User.active_students_in_course_ids([launch_context_id])
                 else
                   User.none
                 end

    base_query.find_by(canvas_id: canvas_id)
  end

  def calendar_params
    @calendar_params ||= params.permit(
      :start_date, :end_date, :type, :undated, :filter, include: [], context_codes: []
    ).to_h.symbolize_keys
  end
end
