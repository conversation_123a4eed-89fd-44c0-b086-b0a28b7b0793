# frozen_string_literal: true

class Api::V1::Students::CalendarsController < ApplicationController
  before_action :set_student

  def all_events
    authorize! :read, User

    normalized_events = []

    # Call EventsService 3 times with different types
    [nil, 'assignment', 'sub_assignment'].each do |event_type|
      service_params = calendar_params.merge(type: event_type)
      service = Calendar::EventsService.new(@student.canvas_id, service_params)
      events = service.call

      events.each do |event|
        normalized_events << normalize_event(event, 'calendar_event')
      end
    end

    # Call PlannerNotesService
    planner_service = Calendar::PlannerNotesService.new(@student.canvas_id, calendar_params)
    planner_notes = planner_service.call

    planner_notes.each do |note|
      normalized_events << normalize_planner_note(note)
    end

    # Call ItemsService
    items_service = Calendar::ItemsService.new(@student.canvas_id, calendar_params)
    items = items_service.call

    items.each do |item|
      normalized_events << normalize_item(item)
    end

    @all_events = normalized_events
  end

  # TODO: May not be used
  def events
    authorize! :read, User

    service = Calendar::EventsService.new(@student.canvas_id, calendar_params)
    @events = service.call
  end

  # TODO: May not be used
  def planner_notes
    authorize! :read, User

    service = Calendar::PlannerNotesService.new(@student.canvas_id, calendar_params)
    @planner_notes = service.call
  end

  # TODO: May not be used
  def items
    authorize! :read, User

    service = Calendar::ItemsService.new(@student.canvas_id, calendar_params)
    @items = service.call
  end

  private

  def set_student
    @student = find_student_by_canvas_id(params[:student_id])

    return if @student

    render json: { error: 'Student not found' }, status: :not_found
    nil
  end

  def find_student_by_canvas_id(canvas_id)
    launch_context_id = current_ability.launch_context.canvas_id
    base_query = if current_ability.launch_context.is_a?(Account)
                   User.active_students_for_account(launch_context_id)
                 elsif current_ability.launch_context.is_a?(Course)
                   User.active_students_in_course_ids([launch_context_id])
                 else
                   User.none
                 end

    base_query.find_by(canvas_id: canvas_id)
  end

  def calendar_params
    params.permit(
      :start_date, :end_date, :type, :undated, :filter, include: [], context_codes: []
    ).to_h.symbolize_keys
  end

  def normalize_event(event, source_type = 'calendar_event')
    title = get_value(event, 'title')
    context_code = get_value(event, 'context_code')

    {
      name: title,
      description: get_value(event, 'description'),
      title: title,
      location: get_value(event, 'location_name'),
      context_id: extract_context_id(context_code),
      context_code: context_code,
      start_date: get_value(event, 'start_at'),
      end_date: get_value(event, 'end_at'),
      plannable_type: source_type,
      plannable_date: get_value(event, 'start_at'),
      context: get_value(event, 'context_name'),
      todo_date: nil
    }
  end

  def normalize_planner_note(note)
    title = get_value(note, 'title')
    todo_date = get_value(note, 'todo_date')
    course_id = get_value(note, 'course_id')

    {
      name: title,
      description: get_value(note, 'details'),
      title: title,
      location: nil,
      context_id: course_id,
      context_code: course_id ? "course_#{course_id}" : nil,
      start_date: todo_date,
      end_date: todo_date,
      plannable_type: 'planner_note',
      plannable_date: todo_date,
      context: nil,
      todo_date: todo_date
    }
  end

  def normalize_item(item)
    plannable = get_value(item, 'plannable') || {}

    {
      name: get_value(plannable, 'title'),
      description: get_value(plannable, 'description'),
      title: get_value(plannable, 'title'),
      location: get_value(plannable, 'location_name'),
      context_id: get_value(item, 'course_id') || get_value(item, 'user_id'),
      context_code: determine_context_code(item),
      start_date: get_value(plannable, 'start_at') || get_value(plannable, 'due_at'),
      end_date: get_value(plannable, 'end_at') || get_value(plannable, 'due_at'),
      plannable_type: get_value(item, 'plannable_type'),
      plannable_date: get_value(item, 'plannable_date'),
      context: get_value(item, 'context_name'),
      todo_date: get_value(plannable, 'todo_date')
    }
  end

  def extract_context_id(context_code)
    return nil unless context_code

    # Extract ID from context codes like "course_123", "user_456"
    context_code.to_s.split('_').last&.to_i
  end

  def determine_context_code(item)
    if item['course_id'] || item[:course_id]
      "course_#{item['course_id'] || item[:course_id]}"
    elsif item['user_id'] || item[:user_id]
      "user_#{item['user_id'] || item[:user_id]}"
    else
      nil
    end
  end
end
