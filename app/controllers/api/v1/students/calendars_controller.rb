# frozen_string_literal: true

class Api::V1::Students::CalendarsController < ApplicationController
  before_action :set_student

  def all_events
    authorize! :read, User

    normalized_events = []

    # Call EventsService 3 times with different types
    event_types = %w[assignment sub_assignment]
    event_types.push(nil) unless [true, 'true', 1, '1'].include?(calendar_params[:undated])
    event_types.each do |event_type|
      service_params = calendar_params.merge(type: event_type)
      service = Calendar::EventsService.new(@student.canvas_id, service_params)
      events = service.call

      events.each do |event|
        normalized_events << {
          context_code: event[:context_code],
          context_name: event[:context_name],
          title: event[:title],
          description: event[:description],
          workflow_state: event[:workflow_state],
          date: event[:start_at],
          start_at: event[:start_at],
          end_at: event[:end_at],
          location_name: event[:location_name],
          location_address: event[:location_address],
          type: 'event',
          html_url: event[:html_url],
          color_key: event[:context_code]
        }
      end
    end

    # Call PlannerNotesService
    unless [true, 'true', 1, '1'].include?(calendar_params[:undated])
      planner_service = Calendar::PlannerNotesService.new(@student.canvas_id, calendar_params)
      planner_notes = planner_service.call

      planner_notes.each do |note|
        context_code = note[:course_id] ? "course_#{note[:course_id]}" : "user_#{note[:user_id]}"

        normalized_events << {
          id: note[:id],
          user_id: note[:user_id],
          course_id: note[:course_id],
          date: note[:todo_date],
          title: note[:title],
          description: note[:details],
          workflow_state: note[:workflow_state],
          context_code: context_code,
          type: 'planner_note',
          color_key: context_code
        }
      end
    end

    # Call ItemsService
    item_filters = %w[ungraded_todo_items all_ungraded_todo_items]
    item_filters.each do |filter|
      service_params = calendar_params.merge(filter: filter)
      service_params[:context_codes] ||= []
      if filter == 'all_ungraded_todo_items'
        service_params[:context_codes].select! { _1.start_with?("user_") }
      elsif filter == 'ungraded_todo_items'
        service_params[:context_codes].select! { _1.start_with?("course_") }
      end
      items_service = Calendar::ItemsService.new(@student.canvas_id, service_params)
      items = items_service.call

      items.each do |item|
        plannable = item[:plannable] || {}

        # Determine context_code based on context_type
        context_code = item[:context_type] == 'Course' ? "course_#{item[:course_id]}" : "user_#{@student.id}"

        normalized_events << {
          context_type: item[:context_type],
          course_id: item[:course_id],
          context_name: item[:context_name],
          context_code: context_code,
          date: item[:plannable_date] || plannable[:due_at],
          html_url: item[:html_url],
          id: item[:plannable_id],
          title: plannable[:title],
          description: plannable[:description] || plannable[:details],
          user_id: plannable[:user_id],
          location_address: plannable[:location_address],
          start_at: plannable[:start_at],
          end_at: plannable[:end_at],
          todo_date: plannable[:due_at],
          type: item[:plannable_type],
          color_key: context_code
        }
      end
    end

    @all_events = normalized_events
  end

  # TODO: May not be used
  def events
    authorize! :read, User

    service = Calendar::EventsService.new(@student.canvas_id, calendar_params)
    @events = service.call
  end

  # TODO: May not be used
  def planner_notes
    authorize! :read, User

    service = Calendar::PlannerNotesService.new(@student.canvas_id, calendar_params)
    @planner_notes = service.call
  end

  # TODO: May not be used
  def items
    authorize! :read, User

    service = Calendar::ItemsService.new(@student.canvas_id, calendar_params)
    @items = service.call
  end

  private

  def set_student
    @student = find_student_by_canvas_id(params[:student_id])

    return if @student

    render json: { error: 'Student not found' }, status: :not_found
    nil
  end

  def find_student_by_canvas_id(canvas_id)
    launch_context_id = current_ability.launch_context.canvas_id
    base_query = if current_ability.launch_context.is_a?(Account)
                   User.active_students_for_account(launch_context_id)
                 elsif current_ability.launch_context.is_a?(Course)
                   User.active_students_in_course_ids([launch_context_id])
                 else
                   User.none
                 end

    base_query.find_by(canvas_id: canvas_id)
  end

  def calendar_params
    @calendar_params ||= params.permit(
      :start_date, :end_date, :type, :undated, :filter, include: [], context_codes: []
    ).to_h.symbolize_keys
  end
end
