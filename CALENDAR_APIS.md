# Calendar APIs Documentation

This document describes the three new Calendar APIs implemented in the `Api::V1::Students::CalendarController`.

## 1. Context Codes API

**Endpoint:** `GET /organizations/:organization_id/api/v1/students/:student_id/calendar/context_codes`

**Description:** Lists all context codes (course IDs and user ID) for a specific student.

**Response Format:**
```json
{
  "context_codes": [
    "course_123",
    "course_456", 
    "user_789"
  ]
}
```

**Implementation Details:**
- Retrieves all active course enrollments for the student
- Formats course IDs as "course_{id}" 
- Includes user context as "user_{student_canvas_id}"
- Scoped to current user's permissions (account admin or course admin)

## 2. Undated Events API

**Endpoint:** `GET /organizations/:organization_id/api/v1/students/:student_id/calendar/undated_events`

**Description:** Lists all undated assignment events for a student by calling multiple Canvas services.

**Response Format:**
```json
{
  "events": [...],
  "planner_notes": [...],
  "items": [...]
}
```

**Implementation Details:**
- Calls `Calendar::EventsService` 3 times with different types:
  - `type: nil` (all events)
  - `type: 'assignment'` (assignment events)
  - `type: 'sub_assignment'` (sub-assignment events)
- Calls `Calendar::PlannerNotesService` once
- Calls `Calendar::ItemsService` once
- All calls include `undated: true` parameter
- Automatically includes context codes for the student's courses

## 3. All Calendar Events API

**Endpoint:** `GET /organizations/:organization_id/api/v1/students/:student_id/calendar/all_events`

**Description:** Lists all calendar events for a student with respect to context codes and date range.

**Parameters:**
- `start_date` (optional): Start date for filtering events
- `end_date` (optional): End date for filtering events
- `type` (optional): Event type filter
- `filter` (optional): Additional filter parameter
- `include[]` (optional): Additional data to include

**Response Format:**
```json
{
  "events": [...],
  "planner_notes": [...], 
  "items": [...]
}
```

**Implementation Details:**
- Calls `Calendar::EventsService` 3 times with different types:
  - `type: nil` (all events)
  - `type: 'assignment'` (assignment events) 
  - `type: 'sub_assignment'` (sub-assignment events)
- Calls `Calendar::PlannerNotesService` once
- Calls `Calendar::ItemsService` once
- Respects date range parameters if provided
- Automatically includes context codes for the student's courses

## Authentication & Authorization

All APIs require:
- Valid session authentication
- User must have `:read` permission on `User` model
- Account admins can access any student in their account
- Course admins can only access students in their courses

## Error Handling

All APIs return:
- `404 Not Found` if student is not found or not accessible
- `401 Unauthorized` if user lacks proper permissions

## Testing

Tests are included in `spec/requests/api/v1/students/calendar_spec.rb` covering:
- Successful requests for both account and course admins
- Parameter handling
- Error scenarios
- Service method calls
