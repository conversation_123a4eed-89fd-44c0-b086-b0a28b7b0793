{
    "events": [],
    "planner_notes": [
        {
            "id": 2,
            "todo_date": "2025-09-05T18:29:00Z",
            "title": "TEST TODO Undated",
            "details": "",
            "user_id": 4,
            "course_id": 2,
            "workflow_state": "active",
            "created_at": "2025-09-04T07:18:42Z",
            "updated_at": "2025-09-04T07:18:42Z"
        },
        {
            "id": 1,
            "todo_date": "2025-09-04T18:29:00Z",
            "title": "TEST TODO",
            "details": "",
            "user_id": 4,
            "course_id": null,
            "workflow_state": "active",
            "created_at": "2025-09-04T07:18:26Z",
            "updated_at": "2025-09-04T07:18:26Z"
        }
    ],
    "items": [
        {
            "context_type": "User",
            "user_id": 4,
            "plannable_id": 3,
            "planner_override": null,
            "plannable_type": "calendar_event",
            "new_activity": false,
            "submissions": false,
            "plannable_date": "2025-08-12T18:30:00Z",
            "plannable": {
                "id": 3,
                "title": "Student 1 Event 1",
                "location_name": "",
                "created_at": "2025-08-20T05:31:26Z",
                "updated_at": "2025-08-20T05:31:26Z",
                "all_day": true,
                "location_address": null,
                "description": null,
                "start_at": "2025-08-12T18:30:00Z",
                "end_at": "2025-08-12T18:30:00Z"
            },
            "html_url": "http://canvas.docker/calendar?event_id=3&include_contexts=user_4",
            "context_name": "Student 1"
        },
        {
            "context_type": "User",
            "user_id": 4,
            "plannable_id": 4,
            "planner_override": null,
            "plannable_type": "calendar_event",
            "new_activity": false,
            "submissions": false,
            "plannable_date": "2025-08-15T18:30:00Z",
            "plannable": {
                "id": 4,
                "title": "Student 1 Event 2",
                "location_name": "",
                "created_at": "2025-08-20T05:31:29Z",
                "updated_at": "2025-08-20T05:31:29Z",
                "all_day": true,
                "location_address": null,
                "description": null,
                "start_at": "2025-08-15T18:30:00Z",
                "end_at": "2025-08-15T18:30:00Z"
            },
            "html_url": "http://canvas.docker/calendar?event_id=4&include_contexts=user_4",
            "context_name": "Student 1"
        },
        {
            "context_type": "Course",
            "course_id": 1,
            "plannable_id": 13,
            "planner_override": null,
            "plannable_type": "assignment",
            "new_activity": false,
            "submissions": {
                "submitted": false,
                "excused": false,
                "graded": false,
                "posted_at": null,
                "late": false,
                "missing": true,
                "needs_grading": false,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-08-16T05:59:00Z",
            "plannable": {
                "id": 13,
                "title": "assignment1 Aug",
                "created_at": "2025-09-04T07:14:52Z",
                "updated_at": "2025-09-04T07:15:02Z",
                "points_possible": 10.0,
                "due_at": "2025-08-16T05:59:00Z"
            },
            "html_url": "/courses/1/assignments/13",
            "context_name": "Shard1 Course",
            "context_image": null
        },
        {
            "context_type": "User",
            "user_id": 4,
            "plannable_id": 2,
            "planner_override": null,
            "plannable_type": "calendar_event",
            "new_activity": false,
            "submissions": false,
            "plannable_date": "2025-08-20T18:30:00Z",
            "plannable": {
                "id": 2,
                "title": "21 August 2025 Event",
                "location_name": "",
                "created_at": "2025-08-18T12:38:36Z",
                "updated_at": "2025-08-18T12:38:52Z",
                "all_day": true,
                "location_address": null,
                "description": null,
                "start_at": "2025-08-20T18:30:00Z",
                "end_at": "2025-08-20T18:30:00Z"
            },
            "html_url": "http://canvas.docker/calendar?event_id=2&include_contexts=user_4",
            "context_name": "Student 1"
        },
        {
            "context_type": "User",
            "user_id": 4,
            "plannable_id": 1,
            "planner_override": null,
            "plannable_type": "calendar_event",
            "new_activity": false,
            "submissions": false,
            "plannable_date": "2025-08-30T18:30:00Z",
            "plannable": {
                "id": 1,
                "title": "31 August 2025 Event",
                "location_name": "",
                "created_at": "2025-08-18T12:38:27Z",
                "updated_at": "2025-08-18T12:38:46Z",
                "all_day": true,
                "location_address": null,
                "description": null,
                "start_at": "2025-08-30T18:30:00Z",
                "end_at": "2025-08-30T18:30:00Z"
            },
            "html_url": "http://canvas.docker/calendar?event_id=1&include_contexts=user_4",
            "context_name": "Student 1"
        },
        {
            "context_type": "Course",
            "course_id": 2,
            "plannable_id": 4,
            "planner_override": null,
            "plannable_type": "quiz",
            "new_activity": false,
            "submissions": {
                "submitted": false,
                "excused": false,
                "graded": false,
                "posted_at": null,
                "late": false,
                "missing": true,
                "needs_grading": false,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-08-31T05:59:59Z",
            "plannable": {
                "id": 4,
                "title": "GradedSurvey1",
                "created_at": "2025-08-18T12:32:46Z",
                "updated_at": "2025-08-18T12:34:24Z",
                "assignment_id": 4,
                "points_possible": 10.0,
                "due_at": "2025-08-31T05:59:59Z"
            },
            "html_url": "/courses/2/quizzes/4",
            "context_name": "Course 1",
            "context_image": null
        },
        {
            "context_type": "Course",
            "course_id": 1,
            "plannable_id": 11,
            "planner_override": null,
            "plannable_type": "assignment",
            "new_activity": false,
            "submissions": {
                "submitted": true,
                "excused": false,
                "graded": false,
                "posted_at": null,
                "late": true,
                "missing": false,
                "needs_grading": true,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-09-03T05:59:00Z",
            "plannable": {
                "id": 11,
                "title": "assignment1 Ungraded",
                "created_at": "2025-09-04T07:09:42Z",
                "updated_at": "2025-09-04T07:09:58Z",
                "points_possible": 10.0,
                "due_at": "2025-09-03T05:59:00Z"
            },
            "html_url": "/courses/1/assignments/11/submissions/4",
            "context_name": "Shard1 Course",
            "context_image": null
        },
        {
            "context_type": "Course",
            "course_id": 1,
            "plannable_id": 12,
            "planner_override": null,
            "plannable_type": "assignment",
            "new_activity": true,
            "submissions": {
                "submitted": true,
                "excused": false,
                "graded": true,
                "posted_at": "2025-09-04T07:11:12Z",
                "late": true,
                "missing": false,
                "needs_grading": false,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-09-03T05:59:00Z",
            "plannable": {
                "id": 12,
                "title": "assignment1 Graded",
                "created_at": "2025-09-04T07:10:01Z",
                "updated_at": "2025-09-04T07:11:12Z",
                "points_possible": 10.0,
                "due_at": "2025-09-03T05:59:00Z"
            },
            "html_url": "/courses/1/assignments/12/submissions/4",
            "context_name": "Shard1 Course",
            "context_image": null
        },
        {
            "context_type": "User",
            "user_id": 4,
            "plannable_id": 5,
            "planner_override": null,
            "plannable_type": "calendar_event",
            "new_activity": false,
            "submissions": false,
            "plannable_date": "2025-09-03T19:15:00Z",
            "plannable": {
                "id": 5,
                "title": "TEST 1",
                "location_name": "",
                "created_at": "2025-09-03T04:52:52Z",
                "updated_at": "2025-09-04T07:08:06Z",
                "all_day": false,
                "location_address": null,
                "description": null,
                "start_at": "2025-09-03T19:15:00Z",
                "end_at": "2025-09-03T22:30:00Z"
            },
            "html_url": "http://canvas.docker/calendar?event_id=5&include_contexts=user_4",
            "context_name": "Student 1"
        },
        {
            "context_type": "User",
            "user_id": 4,
            "plannable_id": 84,
            "planner_override": null,
            "plannable_type": "calendar_event",
            "new_activity": false,
            "submissions": false,
            "plannable_date": "2025-09-03T19:15:00Z",
            "plannable": {
                "id": 84,
                "title": "Deserunt eum explica",
                "location_name": "",
                "created_at": "2025-09-04T07:08:14Z",
                "updated_at": "2025-09-04T07:08:14Z",
                "all_day": false,
                "location_address": null,
                "description": null,
                "start_at": "2025-09-03T19:15:00Z",
                "end_at": "2025-09-03T21:00:00Z"
            },
            "html_url": "http://canvas.docker/calendar?event_id=84&include_contexts=user_4",
            "context_name": "Student 1"
        },
        {
            "context_type": "User",
            "user_id": 4,
            "plannable_id": 83,
            "planner_override": null,
            "plannable_type": "calendar_event",
            "new_activity": false,
            "submissions": false,
            "plannable_date": "2025-09-04T07:07:43Z",
            "plannable": {
                "id": 83,
                "title": "Test Event M stridelearning.beta",
                "location_name": "",
                "created_at": "2025-09-04T07:07:43Z",
                "updated_at": "2025-09-04T07:07:43Z",
                "all_day": false,
                "location_address": null,
                "description": null,
                "start_at": "2025-09-04T07:07:43Z",
                "end_at": "2025-09-04T07:07:43Z"
            },
            "html_url": "http://canvas.docker/calendar?event_id=83&include_contexts=user_4",
            "context_name": "Student 1"
        },
        {
            "context_type": "Course",
            "course_id": 1,
            "plannable_id": 3,
            "planner_override": null,
            "plannable_type": "announcement",
            "new_activity": true,
            "submissions": false,
            "plannable_date": "2025-09-04T07:12:14Z",
            "plannable": {
                "id": 3,
                "title": "Shard 1 Announcement",
                "unread_count": 0,
                "read_state": "unread",
                "created_at": "2025-09-04T07:12:14Z",
                "updated_at": "2025-09-04T07:12:31Z"
            },
            "html_url": "/courses/1/discussion_topics/3",
            "context_name": "Shard1 Course",
            "context_image": null
        },
        {
            "context_type": "Course",
            "course_id": 1,
            "plannable_id": 4,
            "planner_override": null,
            "plannable_type": "announcement",
            "new_activity": true,
            "submissions": false,
            "plannable_date": "2025-09-04T07:13:34Z",
            "plannable": {
                "id": 4,
                "title": "Shard 2 Announcement",
                "unread_count": 0,
                "read_state": "unread",
                "created_at": "2025-09-04T07:13:34Z",
                "updated_at": "2025-09-04T07:13:34Z"
            },
            "html_url": "/courses/1/discussion_topics/4",
            "context_name": "Shard1 Course",
            "context_image": null
        },
        {
            "context_type": "User",
            "user_id": 4,
            "plannable_id": 85,
            "planner_override": null,
            "plannable_type": "calendar_event",
            "new_activity": false,
            "submissions": false,
            "plannable_date": "2025-09-04T08:48:21Z",
            "plannable": {
                "id": 85,
                "title": "k5Course-M3",
                "location_name": "",
                "created_at": "2025-09-04T08:48:23Z",
                "updated_at": "2025-09-04T08:48:23Z",
                "all_day": false,
                "location_address": null,
                "description": null,
                "start_at": "2025-09-04T08:48:21Z",
                "end_at": "2025-09-04T08:48:21Z"
            },
            "html_url": "http://canvas.docker/calendar?event_id=85&include_contexts=user_4",
            "context_name": "Student 1"
        },
        {
            "plannable_id": 1,
            "planner_override": null,
            "plannable_type": "planner_note",
            "new_activity": false,
            "submissions": false,
            "plannable_date": "2025-09-04T18:29:00Z",
            "plannable": {
                "id": 1,
                "title": "TEST TODO",
                "course_id": null,
                "todo_date": "2025-09-04T18:29:00Z",
                "details": "",
                "created_at": "2025-09-04T07:18:26Z",
                "updated_at": "2025-09-04T07:18:26Z",
                "user_id": 4
            }
        },
        {
            "context_type": "Course",
            "course_id": 2,
            "plannable_id": 1,
            "planner_override": null,
            "plannable_type": "discussion_topic",
            "new_activity": true,
            "submissions": {
                "submitted": false,
                "excused": false,
                "graded": false,
                "posted_at": null,
                "late": false,
                "missing": false,
                "needs_grading": false,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-09-05T05:59:59Z",
            "plannable": {
                "id": 1,
                "title": "discussion1",
                "todo_date": null,
                "unread_count": 0,
                "read_state": "unread",
                "created_at": "2025-08-18T12:32:44Z",
                "updated_at": "2025-09-03T04:53:49Z",
                "assignment_id": 2,
                "points_possible": 10.0,
                "due_at": "2025-09-05T05:59:59Z"
            },
            "html_url": "/courses/2/discussion_topics/1",
            "context_name": "Course 1",
            "context_image": null
        },
        {
            "plannable_id": 2,
            "planner_override": null,
            "plannable_type": "planner_note",
            "new_activity": false,
            "submissions": false,
            "plannable_date": "2025-09-05T18:29:00Z",
            "plannable": {
                "id": 2,
                "title": "TEST TODO Undated",
                "course_id": 2,
                "todo_date": "2025-09-05T18:29:00Z",
                "details": "",
                "created_at": "2025-09-04T07:18:42Z",
                "updated_at": "2025-09-04T07:18:42Z",
                "user_id": 4
            },
            "context_name": "Course 1",
            "context_image": null
        },
        {
            "context_type": "User",
            "user_id": 4,
            "plannable_id": 6,
            "planner_override": null,
            "plannable_type": "calendar_event",
            "new_activity": false,
            "submissions": false,
            "plannable_date": "2025-09-05T18:30:00Z",
            "plannable": {
                "id": 6,
                "title": "Test 2",
                "location_name": "",
                "created_at": "2025-09-03T04:52:56Z",
                "updated_at": "2025-09-03T04:52:56Z",
                "all_day": true,
                "location_address": null,
                "description": null,
                "start_at": "2025-09-05T18:30:00Z",
                "end_at": "2025-09-05T18:30:00Z"
            },
            "html_url": "http://canvas.docker/calendar?event_id=6&include_contexts=user_4",
            "context_name": "Student 1"
        },
        {
            "context_type": "Course",
            "course_id": 1,
            "plannable_id": 6,
            "planner_override": null,
            "plannable_type": "assignment",
            "new_activity": false,
            "submissions": {
                "submitted": false,
                "excused": false,
                "graded": false,
                "posted_at": null,
                "late": false,
                "missing": false,
                "needs_grading": false,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-09-11T05:59:00Z",
            "plannable": {
                "id": 6,
                "title": "assignment1 Group",
                "created_at": "2025-09-04T07:06:38Z",
                "updated_at": "2025-09-04T07:21:23Z",
                "points_possible": 10.0,
                "due_at": "2025-09-11T05:59:00Z"
            },
            "html_url": "/courses/1/assignments/6",
            "context_name": "Shard1 Course",
            "context_image": null
        },
        {
            "context_type": "Course",
            "course_id": 2,
            "plannable_id": 3,
            "planner_override": null,
            "plannable_type": "assignment",
            "new_activity": false,
            "submissions": {
                "submitted": false,
                "excused": false,
                "graded": false,
                "posted_at": null,
                "late": false,
                "missing": false,
                "needs_grading": false,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-09-12T05:59:00Z",
            "plannable": {
                "id": 3,
                "title": "assignment1",
                "created_at": "2025-08-18T12:32:44Z",
                "updated_at": "2025-09-03T04:53:59Z",
                "points_possible": 10.0,
                "due_at": "2025-09-12T05:59:00Z"
            },
            "html_url": "/courses/2/assignments/3",
            "context_name": "Course 1",
            "context_image": null
        },
        {
            "context_type": "Course",
            "course_id": 3,
            "plannable_id": 5,
            "planner_override": null,
            "plannable_type": "discussion_topic",
            "new_activity": true,
            "submissions": false,
            "plannable_date": "2025-09-12T05:59:59Z",
            "plannable": {
                "id": 5,
                "title": "Reprehenderit autem reprehenderit id repellendus Vel sunt",
                "todo_date": "2025-09-12T05:59:59Z",
                "unread_count": 0,
                "read_state": "unread",
                "created_at": "2025-09-04T07:56:46Z",
                "updated_at": "2025-09-04T07:56:54Z",
                "assignment_id": null
            },
            "html_url": "/courses/3/discussion_topics/5",
            "context_name": "Test Course 2",
            "context_image": null
        },
        {
            "context_type": "Course",
            "course_id": 1,
            "plannable_id": 2,
            "planner_override": null,
            "plannable_type": "discussion_topic",
            "new_activity": true,
            "submissions": {
                "submitted": false,
                "excused": false,
                "graded": false,
                "posted_at": null,
                "late": false,
                "missing": false,
                "needs_grading": false,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-09-16T05:59:00Z",
            "plannable": {
                "id": 2,
                "title": "discussion1",
                "todo_date": null,
                "unread_count": 0,
                "read_state": "unread",
                "created_at": "2025-09-04T07:06:39Z",
                "updated_at": "2025-09-04T07:07:12Z",
                "assignment_id": 9,
                "points_possible": 10.0,
                "due_at": "2025-09-16T05:59:00Z"
            },
            "html_url": "/courses/1/discussion_topics/2",
            "context_name": "Shard1 Course",
            "context_image": null
        },
        {
            "context_type": "Course",
            "course_id": 1,
            "plannable_id": 7,
            "planner_override": null,
            "plannable_type": "quiz",
            "new_activity": false,
            "submissions": {
                "submitted": false,
                "excused": false,
                "graded": false,
                "posted_at": null,
                "late": false,
                "missing": false,
                "needs_grading": false,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-09-20T05:59:00Z",
            "plannable": {
                "id": 7,
                "title": "GradedQuiz1",
                "created_at": "2025-09-04T07:06:39Z",
                "updated_at": "2025-09-04T07:06:58Z",
                "assignment_id": 7,
                "points_possible": 58.0,
                "due_at": "2025-09-20T05:59:00Z"
            },
            "html_url": "/courses/1/quizzes/7",
            "context_name": "Shard1 Course",
            "context_image": null
        },
        {
            "context_type": "Course",
            "course_id": 3,
            "plannable_id": 34,
            "planner_override": null,
            "plannable_type": "assignment",
            "new_activity": false,
            "submissions": {
                "submitted": false,
                "excused": false,
                "graded": false,
                "posted_at": null,
                "late": false,
                "missing": false,
                "needs_grading": false,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-09-20T05:59:00Z",
            "plannable": {
                "id": 34,
                "title": "15.01 Quiz: Diagnostic Assessment More Than 150 Questions OLS",
                "created_at": "2025-09-04T07:55:43Z",
                "updated_at": "2025-09-04T07:56:19Z",
                "points_possible": 51.0,
                "due_at": "2025-09-20T05:59:00Z"
            },
            "html_url": "/courses/3/assignments/34",
            "context_name": "Test Course 2",
            "context_image": null
        },
        {
            "context_type": "Course",
            "course_id": 2,
            "plannable_id": 1,
            "planner_override": null,
            "plannable_type": "quiz",
            "new_activity": false,
            "submissions": {
                "submitted": false,
                "excused": false,
                "graded": false,
                "posted_at": null,
                "late": false,
                "missing": false,
                "needs_grading": false,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-09-20T05:59:59Z",
            "plannable": {
                "id": 1,
                "title": "GradedQuiz1",
                "created_at": "2025-08-18T12:32:44Z",
                "updated_at": "2025-09-03T04:54:14Z",
                "assignment_id": 1,
                "points_possible": 58.0,
                "due_at": "2025-09-20T05:59:59Z"
            },
            "html_url": "/courses/2/quizzes/1",
            "context_name": "Course 1",
            "context_image": null
        },
        {
            "context_type": "Course",
            "course_id": 1,
            "plannable_id": 8,
            "planner_override": null,
            "plannable_type": "quiz",
            "new_activity": false,
            "submissions": {
                "submitted": false,
                "excused": false,
                "graded": false,
                "posted_at": null,
                "late": false,
                "missing": false,
                "needs_grading": false,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-09-21T05:59:00Z",
            "plannable": {
                "id": 8,
                "title": "GradedSurvey1",
                "created_at": "2025-09-04T07:06:39Z",
                "updated_at": "2025-09-04T07:07:04Z",
                "assignment_id": 8,
                "points_possible": null,
                "due_at": "2025-09-21T05:59:00Z"
            },
            "html_url": "/courses/1/quizzes/8",
            "context_name": "Shard1 Course",
            "context_image": null
        },
        {
            "context_type": "Course",
            "course_id": 1,
            "plannable_id": 14,
            "planner_override": null,
            "plannable_type": "assignment",
            "new_activity": false,
            "submissions": {
                "submitted": false,
                "excused": false,
                "graded": false,
                "posted_at": null,
                "late": false,
                "missing": false,
                "needs_grading": false,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-10-26T05:59:00Z",
            "plannable": {
                "id": 14,
                "title": "assignment1 Oct",
                "created_at": "2025-09-04T07:15:06Z",
                "updated_at": "2025-09-04T07:15:20Z",
                "points_possible": 10.0,
                "due_at": "2025-10-26T05:59:00Z"
            },
            "html_url": "/courses/1/assignments/14",
            "context_name": "Shard1 Course",
            "context_image": null
        }
    ]
}


{
    "events": [
        {
            "id": 5,
            "title": "TEST 1",
            "start_at": "2025-09-03T19:15:00Z",
            "end_at": "2025-09-03T22:30:00Z",
            "workflow_state": "active",
            "created_at": "2025-09-03T04:52:52Z",
            "updated_at": "2025-09-04T07:08:06Z",
            "all_day": false,
            "all_day_date": "2025-09-04",
            "comments": null,
            "rrule": "",
            "series_uuid": null,
            "blackout_date": false,
            "location_address": null,
            "location_name": "",
            "type": "event",
            "description": null,
            "child_events_count": 0,
            "all_context_codes": "user_4",
            "context_code": "user_4",
            "context_name": "Student 1",
            "context_color": null,
            "parent_event_id": null,
            "hidden": false,
            "child_events": [],
            "url": "http://canvas.docker/api/v1/calendar_events/5",
            "html_url": "http://canvas.docker/calendar?event_id=5&include_contexts=user_4",
            "duplicates": [],
            "important_dates": false
        },
        {
            "id": 84,
            "title": "Deserunt eum explica",
            "start_at": "2025-09-03T19:15:00Z",
            "end_at": "2025-09-03T21:00:00Z",
            "workflow_state": "active",
            "created_at": "2025-09-04T07:08:14Z",
            "updated_at": "2025-09-04T07:08:14Z",
            "all_day": false,
            "all_day_date": null,
            "comments": null,
            "rrule": "",
            "series_uuid": null,
            "blackout_date": false,
            "location_address": null,
            "location_name": "",
            "type": "event",
            "description": null,
            "child_events_count": 0,
            "all_context_codes": "user_4",
            "context_code": "user_4",
            "context_name": "Student 1",
            "context_color": null,
            "parent_event_id": null,
            "hidden": false,
            "child_events": [],
            "url": "http://canvas.docker/api/v1/calendar_events/84",
            "html_url": "http://canvas.docker/calendar?event_id=84&include_contexts=user_4",
            "duplicates": [],
            "important_dates": false
        },
        {
            "id": 83,
            "title": "Test Event M stridelearning.beta",
            "start_at": "2025-09-04T07:07:43Z",
            "end_at": "2025-09-04T07:07:43Z",
            "workflow_state": "active",
            "created_at": "2025-09-04T07:07:43Z",
            "updated_at": "2025-09-04T07:07:43Z",
            "all_day": false,
            "all_day_date": null,
            "comments": null,
            "rrule": "",
            "series_uuid": null,
            "blackout_date": false,
            "location_address": null,
            "location_name": "",
            "type": "event",
            "description": null,
            "child_events_count": 0,
            "all_context_codes": "user_4",
            "context_code": "user_4",
            "context_name": "Student 1",
            "context_color": null,
            "parent_event_id": null,
            "hidden": false,
            "child_events": [],
            "url": "http://canvas.docker/api/v1/calendar_events/83",
            "html_url": "http://canvas.docker/calendar?event_id=83&include_contexts=user_4",
            "duplicates": [],
            "important_dates": false
        },
        {
            "id": 85,
            "title": "k5Course-M3",
            "start_at": "2025-09-04T08:48:21Z",
            "end_at": "2025-09-04T08:48:21Z",
            "workflow_state": "active",
            "created_at": "2025-09-04T08:48:23Z",
            "updated_at": "2025-09-04T08:48:23Z",
            "all_day": false,
            "all_day_date": null,
            "comments": null,
            "rrule": "",
            "series_uuid": null,
            "blackout_date": false,
            "location_address": null,
            "location_name": "",
            "type": "event",
            "description": null,
            "child_events_count": 0,
            "all_context_codes": "user_4",
            "context_code": "user_4",
            "context_name": "Student 1",
            "context_color": null,
            "parent_event_id": null,
            "hidden": false,
            "child_events": [],
            "url": "http://canvas.docker/api/v1/calendar_events/85",
            "html_url": "http://canvas.docker/calendar?event_id=85&include_contexts=user_4",
            "duplicates": [],
            "important_dates": false
        },
        {
            "id": 6,
            "title": "Test 2",
            "start_at": "2025-09-05T18:30:00Z",
            "end_at": "2025-09-05T18:30:00Z",
            "workflow_state": "active",
            "created_at": "2025-09-03T04:52:56Z",
            "updated_at": "2025-09-03T04:52:56Z",
            "all_day": true,
            "all_day_date": "2025-09-06",
            "comments": null,
            "rrule": "",
            "series_uuid": null,
            "blackout_date": false,
            "location_address": null,
            "location_name": "",
            "type": "event",
            "description": null,
            "child_events_count": 0,
            "all_context_codes": "user_4",
            "context_code": "user_4",
            "context_name": "Student 1",
            "context_color": null,
            "parent_event_id": null,
            "hidden": false,
            "child_events": [],
            "url": "http://canvas.docker/api/v1/calendar_events/6",
            "html_url": "http://canvas.docker/calendar?event_id=6&include_contexts=user_4",
            "duplicates": [],
            "important_dates": false
        }
    ],
    "planner_notes": [
        {
            "id": 2,
            "todo_date": "2025-09-05T18:29:00Z",
            "title": "TEST TODO Undated",
            "details": "",
            "user_id": 4,
            "course_id": 2,
            "workflow_state": "active",
            "created_at": "2025-09-04T07:18:42Z",
            "updated_at": "2025-09-04T07:18:42Z"
        },
        {
            "id": 1,
            "todo_date": "2025-09-04T18:29:00Z",
            "title": "TEST TODO",
            "details": "",
            "user_id": 4,
            "course_id": null,
            "workflow_state": "active",
            "created_at": "2025-09-04T07:18:26Z",
            "updated_at": "2025-09-04T07:18:26Z"
        }
    ],
    "items": [
        {
            "context_type": "Course",
            "course_id": 1,
            "plannable_id": 11,
            "planner_override": null,
            "plannable_type": "assignment",
            "new_activity": false,
            "submissions": {
                "submitted": true,
                "excused": false,
                "graded": false,
                "posted_at": null,
                "late": true,
                "missing": false,
                "needs_grading": true,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-09-03T05:59:00Z",
            "plannable": {
                "id": 11,
                "title": "assignment1 Ungraded",
                "created_at": "2025-09-04T07:09:42Z",
                "updated_at": "2025-09-04T07:09:58Z",
                "points_possible": 10.0,
                "due_at": "2025-09-03T05:59:00Z"
            },
            "html_url": "/courses/1/assignments/11/submissions/4",
            "context_name": "Shard1 Course",
            "context_image": null
        },
        {
            "context_type": "Course",
            "course_id": 1,
            "plannable_id": 12,
            "planner_override": null,
            "plannable_type": "assignment",
            "new_activity": true,
            "submissions": {
                "submitted": true,
                "excused": false,
                "graded": true,
                "posted_at": "2025-09-04T07:11:12Z",
                "late": true,
                "missing": false,
                "needs_grading": false,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-09-03T05:59:00Z",
            "plannable": {
                "id": 12,
                "title": "assignment1 Graded",
                "created_at": "2025-09-04T07:10:01Z",
                "updated_at": "2025-09-04T07:11:12Z",
                "points_possible": 10.0,
                "due_at": "2025-09-03T05:59:00Z"
            },
            "html_url": "/courses/1/assignments/12/submissions/4",
            "context_name": "Shard1 Course",
            "context_image": null
        },
        {
            "context_type": "User",
            "user_id": 4,
            "plannable_id": 5,
            "planner_override": null,
            "plannable_type": "calendar_event",
            "new_activity": false,
            "submissions": false,
            "plannable_date": "2025-09-03T19:15:00Z",
            "plannable": {
                "id": 5,
                "title": "TEST 1",
                "location_name": "",
                "created_at": "2025-09-03T04:52:52Z",
                "updated_at": "2025-09-04T07:08:06Z",
                "all_day": false,
                "location_address": null,
                "description": null,
                "start_at": "2025-09-03T19:15:00Z",
                "end_at": "2025-09-03T22:30:00Z"
            },
            "html_url": "http://canvas.docker/calendar?event_id=5&include_contexts=user_4",
            "context_name": "Student 1"
        },
        {
            "context_type": "User",
            "user_id": 4,
            "plannable_id": 84,
            "planner_override": null,
            "plannable_type": "calendar_event",
            "new_activity": false,
            "submissions": false,
            "plannable_date": "2025-09-03T19:15:00Z",
            "plannable": {
                "id": 84,
                "title": "Deserunt eum explica",
                "location_name": "",
                "created_at": "2025-09-04T07:08:14Z",
                "updated_at": "2025-09-04T07:08:14Z",
                "all_day": false,
                "location_address": null,
                "description": null,
                "start_at": "2025-09-03T19:15:00Z",
                "end_at": "2025-09-03T21:00:00Z"
            },
            "html_url": "http://canvas.docker/calendar?event_id=84&include_contexts=user_4",
            "context_name": "Student 1"
        },
        {
            "context_type": "User",
            "user_id": 4,
            "plannable_id": 83,
            "planner_override": null,
            "plannable_type": "calendar_event",
            "new_activity": false,
            "submissions": false,
            "plannable_date": "2025-09-04T07:07:43Z",
            "plannable": {
                "id": 83,
                "title": "Test Event M stridelearning.beta",
                "location_name": "",
                "created_at": "2025-09-04T07:07:43Z",
                "updated_at": "2025-09-04T07:07:43Z",
                "all_day": false,
                "location_address": null,
                "description": null,
                "start_at": "2025-09-04T07:07:43Z",
                "end_at": "2025-09-04T07:07:43Z"
            },
            "html_url": "http://canvas.docker/calendar?event_id=83&include_contexts=user_4",
            "context_name": "Student 1"
        },
        {
            "context_type": "Course",
            "course_id": 1,
            "plannable_id": 3,
            "planner_override": null,
            "plannable_type": "announcement",
            "new_activity": true,
            "submissions": false,
            "plannable_date": "2025-09-04T07:12:14Z",
            "plannable": {
                "id": 3,
                "title": "Shard 1 Announcement",
                "unread_count": 0,
                "read_state": "unread",
                "created_at": "2025-09-04T07:12:14Z",
                "updated_at": "2025-09-04T07:12:31Z"
            },
            "html_url": "/courses/1/discussion_topics/3",
            "context_name": "Shard1 Course",
            "context_image": null
        },
        {
            "context_type": "Course",
            "course_id": 1,
            "plannable_id": 4,
            "planner_override": null,
            "plannable_type": "announcement",
            "new_activity": true,
            "submissions": false,
            "plannable_date": "2025-09-04T07:13:34Z",
            "plannable": {
                "id": 4,
                "title": "Shard 2 Announcement",
                "unread_count": 0,
                "read_state": "unread",
                "created_at": "2025-09-04T07:13:34Z",
                "updated_at": "2025-09-04T07:13:34Z"
            },
            "html_url": "/courses/1/discussion_topics/4",
            "context_name": "Shard1 Course",
            "context_image": null
        },
        {
            "context_type": "User",
            "user_id": 4,
            "plannable_id": 85,
            "planner_override": null,
            "plannable_type": "calendar_event",
            "new_activity": false,
            "submissions": false,
            "plannable_date": "2025-09-04T08:48:21Z",
            "plannable": {
                "id": 85,
                "title": "k5Course-M3",
                "location_name": "",
                "created_at": "2025-09-04T08:48:23Z",
                "updated_at": "2025-09-04T08:48:23Z",
                "all_day": false,
                "location_address": null,
                "description": null,
                "start_at": "2025-09-04T08:48:21Z",
                "end_at": "2025-09-04T08:48:21Z"
            },
            "html_url": "http://canvas.docker/calendar?event_id=85&include_contexts=user_4",
            "context_name": "Student 1"
        },
        {
            "plannable_id": 1,
            "planner_override": null,
            "plannable_type": "planner_note",
            "new_activity": false,
            "submissions": false,
            "plannable_date": "2025-09-04T18:29:00Z",
            "plannable": {
                "id": 1,
                "title": "TEST TODO",
                "course_id": null,
                "todo_date": "2025-09-04T18:29:00Z",
                "details": "",
                "created_at": "2025-09-04T07:18:26Z",
                "updated_at": "2025-09-04T07:18:26Z",
                "user_id": 4
            }
        },
        {
            "context_type": "Course",
            "course_id": 2,
            "plannable_id": 1,
            "planner_override": null,
            "plannable_type": "discussion_topic",
            "new_activity": true,
            "submissions": {
                "submitted": false,
                "excused": false,
                "graded": false,
                "posted_at": null,
                "late": false,
                "missing": false,
                "needs_grading": false,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-09-05T05:59:59Z",
            "plannable": {
                "id": 1,
                "title": "discussion1",
                "todo_date": null,
                "unread_count": 0,
                "read_state": "unread",
                "created_at": "2025-08-18T12:32:44Z",
                "updated_at": "2025-09-03T04:53:49Z",
                "assignment_id": 2,
                "points_possible": 10.0,
                "due_at": "2025-09-05T05:59:59Z"
            },
            "html_url": "/courses/2/discussion_topics/1",
            "context_name": "Course 1",
            "context_image": null
        },
        {
            "plannable_id": 2,
            "planner_override": null,
            "plannable_type": "planner_note",
            "new_activity": false,
            "submissions": false,
            "plannable_date": "2025-09-05T18:29:00Z",
            "plannable": {
                "id": 2,
                "title": "TEST TODO Undated",
                "course_id": 2,
                "todo_date": "2025-09-05T18:29:00Z",
                "details": "",
                "created_at": "2025-09-04T07:18:42Z",
                "updated_at": "2025-09-04T07:18:42Z",
                "user_id": 4
            },
            "context_name": "Course 1",
            "context_image": null
        },
        {
            "context_type": "User",
            "user_id": 4,
            "plannable_id": 6,
            "planner_override": null,
            "plannable_type": "calendar_event",
            "new_activity": false,
            "submissions": false,
            "plannable_date": "2025-09-05T18:30:00Z",
            "plannable": {
                "id": 6,
                "title": "Test 2",
                "location_name": "",
                "created_at": "2025-09-03T04:52:56Z",
                "updated_at": "2025-09-03T04:52:56Z",
                "all_day": true,
                "location_address": null,
                "description": null,
                "start_at": "2025-09-05T18:30:00Z",
                "end_at": "2025-09-05T18:30:00Z"
            },
            "html_url": "http://canvas.docker/calendar?event_id=6&include_contexts=user_4",
            "context_name": "Student 1"
        },
        {
            "context_type": "Course",
            "course_id": 1,
            "plannable_id": 6,
            "planner_override": null,
            "plannable_type": "assignment",
            "new_activity": false,
            "submissions": {
                "submitted": false,
                "excused": false,
                "graded": false,
                "posted_at": null,
                "late": false,
                "missing": false,
                "needs_grading": false,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-09-11T05:59:00Z",
            "plannable": {
                "id": 6,
                "title": "assignment1 Group",
                "created_at": "2025-09-04T07:06:38Z",
                "updated_at": "2025-09-04T07:21:23Z",
                "points_possible": 10.0,
                "due_at": "2025-09-11T05:59:00Z"
            },
            "html_url": "/courses/1/assignments/6",
            "context_name": "Shard1 Course",
            "context_image": null
        },
        {
            "context_type": "Course",
            "course_id": 2,
            "plannable_id": 3,
            "planner_override": null,
            "plannable_type": "assignment",
            "new_activity": false,
            "submissions": {
                "submitted": false,
                "excused": false,
                "graded": false,
                "posted_at": null,
                "late": false,
                "missing": false,
                "needs_grading": false,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-09-12T05:59:00Z",
            "plannable": {
                "id": 3,
                "title": "assignment1",
                "created_at": "2025-08-18T12:32:44Z",
                "updated_at": "2025-09-03T04:53:59Z",
                "points_possible": 10.0,
                "due_at": "2025-09-12T05:59:00Z"
            },
            "html_url": "/courses/2/assignments/3",
            "context_name": "Course 1",
            "context_image": null
        },
        {
            "context_type": "Course",
            "course_id": 3,
            "plannable_id": 5,
            "planner_override": null,
            "plannable_type": "discussion_topic",
            "new_activity": true,
            "submissions": false,
            "plannable_date": "2025-09-12T05:59:59Z",
            "plannable": {
                "id": 5,
                "title": "Reprehenderit autem reprehenderit id repellendus Vel sunt",
                "todo_date": "2025-09-12T05:59:59Z",
                "unread_count": 0,
                "read_state": "unread",
                "created_at": "2025-09-04T07:56:46Z",
                "updated_at": "2025-09-04T07:56:54Z",
                "assignment_id": null
            },
            "html_url": "/courses/3/discussion_topics/5",
            "context_name": "Test Course 2",
            "context_image": null
        },
        {
            "context_type": "Course",
            "course_id": 1,
            "plannable_id": 2,
            "planner_override": null,
            "plannable_type": "discussion_topic",
            "new_activity": true,
            "submissions": {
                "submitted": false,
                "excused": false,
                "graded": false,
                "posted_at": null,
                "late": false,
                "missing": false,
                "needs_grading": false,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-09-16T05:59:00Z",
            "plannable": {
                "id": 2,
                "title": "discussion1",
                "todo_date": null,
                "unread_count": 0,
                "read_state": "unread",
                "created_at": "2025-09-04T07:06:39Z",
                "updated_at": "2025-09-04T07:07:12Z",
                "assignment_id": 9,
                "points_possible": 10.0,
                "due_at": "2025-09-16T05:59:00Z"
            },
            "html_url": "/courses/1/discussion_topics/2",
            "context_name": "Shard1 Course",
            "context_image": null
        },
        {
            "context_type": "Course",
            "course_id": 1,
            "plannable_id": 7,
            "planner_override": null,
            "plannable_type": "quiz",
            "new_activity": false,
            "submissions": {
                "submitted": false,
                "excused": false,
                "graded": false,
                "posted_at": null,
                "late": false,
                "missing": false,
                "needs_grading": false,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-09-20T05:59:00Z",
            "plannable": {
                "id": 7,
                "title": "GradedQuiz1",
                "created_at": "2025-09-04T07:06:39Z",
                "updated_at": "2025-09-04T07:06:58Z",
                "assignment_id": 7,
                "points_possible": 58.0,
                "due_at": "2025-09-20T05:59:00Z"
            },
            "html_url": "/courses/1/quizzes/7",
            "context_name": "Shard1 Course",
            "context_image": null
        },
        {
            "context_type": "Course",
            "course_id": 3,
            "plannable_id": 34,
            "planner_override": null,
            "plannable_type": "assignment",
            "new_activity": false,
            "submissions": {
                "submitted": false,
                "excused": false,
                "graded": false,
                "posted_at": null,
                "late": false,
                "missing": false,
                "needs_grading": false,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-09-20T05:59:00Z",
            "plannable": {
                "id": 34,
                "title": "15.01 Quiz: Diagnostic Assessment More Than 150 Questions OLS",
                "created_at": "2025-09-04T07:55:43Z",
                "updated_at": "2025-09-04T07:56:19Z",
                "points_possible": 51.0,
                "due_at": "2025-09-20T05:59:00Z"
            },
            "html_url": "/courses/3/assignments/34",
            "context_name": "Test Course 2",
            "context_image": null
        },
        {
            "context_type": "Course",
            "course_id": 2,
            "plannable_id": 1,
            "planner_override": null,
            "plannable_type": "quiz",
            "new_activity": false,
            "submissions": {
                "submitted": false,
                "excused": false,
                "graded": false,
                "posted_at": null,
                "late": false,
                "missing": false,
                "needs_grading": false,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-09-20T05:59:59Z",
            "plannable": {
                "id": 1,
                "title": "GradedQuiz1",
                "created_at": "2025-08-18T12:32:44Z",
                "updated_at": "2025-09-03T04:54:14Z",
                "assignment_id": 1,
                "points_possible": 58.0,
                "due_at": "2025-09-20T05:59:59Z"
            },
            "html_url": "/courses/2/quizzes/1",
            "context_name": "Course 1",
            "context_image": null
        },
        {
            "context_type": "Course",
            "course_id": 1,
            "plannable_id": 8,
            "planner_override": null,
            "plannable_type": "quiz",
            "new_activity": false,
            "submissions": {
                "submitted": false,
                "excused": false,
                "graded": false,
                "posted_at": null,
                "late": false,
                "missing": false,
                "needs_grading": false,
                "has_feedback": false,
                "redo_request": false
            },
            "plannable_date": "2025-09-21T05:59:00Z",
            "plannable": {
                "id": 8,
                "title": "GradedSurvey1",
                "created_at": "2025-09-04T07:06:39Z",
                "updated_at": "2025-09-04T07:07:04Z",
                "assignment_id": 8,
                "points_possible": null,
                "due_at": "2025-09-21T05:59:00Z"
            },
            "html_url": "/courses/1/quizzes/8",
            "context_name": "Shard1 Course",
            "context_image": null
        }
    ]
}
This is response.

I need to prepare a common response

Create a single array of objects,

Name, description, title, location, context_id, context_code, start_date, end_date, plannable_type, playable date, context, todo_date,

