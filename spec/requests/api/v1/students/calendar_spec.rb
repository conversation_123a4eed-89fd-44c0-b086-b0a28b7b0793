# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Students::CalendarController', type: :request do
  let(:organization) { current_organization }
  let!(:account) { create(:account) }
  let!(:course) { create(:course, account: account) }
  let!(:other_course) { create(:course, account: account) }

  let!(:account_admin) { create(:admin, account: account).user }
  let!(:course_admin) { create(:enrollment, :teacher, user: create(:user), course: course).user }

  let!(:admin_session) { create(:session, :admin, user: account_admin, account: account, launch_context: account) }
  let!(:course_admin_session) { create(:session, :teacher, user: course_admin, account: account, launch_context: course) }

  # Students in the main course
  let!(:students) { create_list(:user, 3).each { |student| create(:enrollment, :student, user: student, course: course) } }
  # Students in other course (should only be visible to account admin)
  let!(:other_students) { create_list(:user, 2).each { |student| create(:enrollment, :student, user: student, course: other_course) } }

  let(:student) { students.first }

  shared_context 'with account admin session' do
    let(:current_session) { admin_session }
    let(:default_params) { { session_key: admin_session.session_key } }
  end

  shared_context 'with course admin session' do
    let(:current_session) { course_admin_session }
    let(:default_params) { { session_key: course_admin_session.session_key } }
  end

  shared_context 'with mocked canvas responses' do
    let(:mock_events_response) do
      [
        {
          'id' => '1',
          'title' => 'Test Event',
          'start_at' => '2025-01-15T10:00:00Z',
          'end_at' => '2025-01-15T11:00:00Z',
          'context_code' => "user_#{student.canvas_id}"
        }
      ]
    end

    let(:mock_planner_notes_response) do
      [
        {
          'id' => '1',
          'title' => 'Test Note',
          'details' => 'Test note details',
          'todo_date' => '2025-01-15T10:00:00Z'
        }
      ]
    end

    let(:mock_items_response) do
      [
        {
          'plannable_id' => '1',
          'plannable_type' => 'assignment',
          'plannable' => {
            'id' => '1',
            'title' => 'Test Assignment',
            'due_at' => '2025-01-15T23:59:59Z'
          }
        }
      ]
    end

    before do
      allow_any_instance_of(Calendar::EventsService).to receive(:call).and_return(mock_events_response)
      allow_any_instance_of(Calendar::PlannerNotesService).to receive(:call).and_return(mock_planner_notes_response)
      allow_any_instance_of(Calendar::ItemsService).to receive(:call).and_return(mock_items_response)
    end
  end

  describe 'GET /api/v1/students/:student_id/calendar/events' do
    include_context 'with mocked canvas responses'

    shared_examples 'calendar events functionality' do
      context 'successful request' do
        it 'returns calendar events for the student' do
          get events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params

          expect(response).to have_http_status(:ok)
          expect(json_response[:events]).to be_an(Array)
          expect(json_response[:events].first['title']).to eq('Test Event')
        end

        it 'accepts date range parameters' do
          get events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params.merge({
                                             start_date: '2025-01-01T00:00:00Z',
                                             end_date: '2025-01-31T23:59:59Z'
                                           })

          expect(response).to have_http_status(:ok)
          expect(json_response[:events]).to be_an(Array)
        end

        it 'accepts type parameter' do
          get events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params.merge({ type: 'assignment' })

          expect(response).to have_http_status(:ok)
          expect(json_response[:events]).to be_an(Array)
        end

        it 'accepts include parameters' do
          get events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params.merge({ include: %w[web_conference series_head] })

          expect(response).to have_http_status(:ok)
          expect(json_response[:events]).to be_an(Array)
        end
      end

      context 'error handling' do
        it 'returns 404 when student is not found' do
          get events_api_v1_student_calendar_path(organization_id: organization.id, student_id: 99_999),
              params: default_params

          expect(response).to have_http_status(:not_found)
          expect(json_response[:error]).to eq('Student not found')
        end
      end
    end

    context 'when authenticated as account admin' do
      include_context 'with account admin session'
      include_examples 'calendar events functionality'

      it 'can access events for students from any course in the account' do
        other_course_student = other_students.first
        get events_api_v1_student_calendar_path(organization_id: organization.id, student_id: other_course_student.canvas_id),
            params: default_params

        expect(response).to have_http_status(:ok)
        expect(json_response[:events]).to be_an(Array)
      end
    end

    context 'when authenticated as course admin' do
      include_context 'with course admin session'
      include_examples 'calendar events functionality'

      it 'can access events for students in their course' do
        get events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
            params: default_params

        expect(response).to have_http_status(:ok)
        expect(json_response[:events]).to be_an(Array)
      end

      it 'cannot access events for students from other courses' do
        other_course_student = other_students.first
        get events_api_v1_student_calendar_path(organization_id: organization.id, student_id: other_course_student.canvas_id),
            params: default_params

        expect(response).to have_http_status(:not_found)
        expect(json_response[:error]).to eq('Student not found')
      end
    end
  end

  describe 'GET /api/v1/students/:student_id/calendar/planner_notes' do
    include_context 'with mocked canvas responses'

    shared_examples 'planner notes functionality' do
      context 'successful request' do
        it 'returns planner notes for the student' do
          get planner_notes_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params

          expect(response).to have_http_status(:ok)
          expect(json_response[:planner_notes]).to be_an(Array)
          expect(json_response[:planner_notes].first['title']).to eq('Test Note')
        end

        it 'accepts date range parameters' do
          get planner_notes_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params.merge({
                                             start_date: '2025-01-01T00:00:00Z',
                                             end_date: '2025-01-31T23:59:59Z'
                                           })

          expect(response).to have_http_status(:ok)
          expect(json_response[:planner_notes]).to be_an(Array)
        end
      end
    end

    context 'when authenticated as account admin' do
      include_context 'with account admin session'
      include_examples 'planner notes functionality'
    end

    context 'when authenticated as course admin' do
      include_context 'with course admin session'
      include_examples 'planner notes functionality'
    end
  end

  describe 'GET /api/v1/students/:student_id/calendar/items' do
    include_context 'with mocked canvas responses'

    shared_examples 'calendar items functionality' do
      context 'successful request' do
        it 'returns calendar items for the student' do
          get items_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params

          expect(response).to have_http_status(:ok)
          expect(json_response[:items]).to be_an(Array)
          expect(json_response[:items].first['plannable']['title']).to eq('Test Assignment')
        end

        it 'accepts course_ids parameter' do
          get items_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params.merge({ course_ids: [course.canvas_id] })

          expect(response).to have_http_status(:ok)
          expect(json_response[:items]).to be_an(Array)
        end

        it 'accepts filter parameter' do
          get items_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params.merge({ filter: 'new_activity' })

          expect(response).to have_http_status(:ok)
          expect(json_response[:items]).to be_an(Array)
        end
      end
    end

    context 'when authenticated as account admin' do
      include_context 'with account admin session'
      include_examples 'calendar items functionality'
    end

    context 'when authenticated as course admin' do
      include_context 'with course admin session'
      include_examples 'calendar items functionality'
    end
  end

  describe 'GET /api/v1/students/:student_id/calendar/context_codes' do
    shared_examples 'context codes functionality' do
      context 'successful request' do
        it 'returns context codes for the student' do
          get context_codes_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params

          expect(response).to have_http_status(:ok)
          expect(json_response[:context_codes]).to be_an(Array)
          expect(json_response[:context_codes]).to include("course_#{course.canvas_id}")
          expect(json_response[:context_codes]).to include("user_#{student.canvas_id}")
        end
      end

      context 'error handling' do
        it 'returns 404 when student is not found' do
          get context_codes_api_v1_student_calendar_path(organization_id: organization.id, student_id: 99_999),
              params: default_params

          expect(response).to have_http_status(:not_found)
          expect(json_response[:error]).to eq('Student not found')
        end
      end
    end

    context 'when authenticated as account admin' do
      include_context 'with account admin session'
      include_examples 'context codes functionality'
    end

    context 'when authenticated as course admin' do
      include_context 'with course admin session'
      include_examples 'context codes functionality'
    end
  end

  describe 'GET /api/v1/students/:student_id/calendar/undated_events' do
    include_context 'with mocked canvas responses'

    shared_examples 'undated events functionality' do
      context 'successful request' do
        it 'returns undated events for the student' do
          get undated_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params

          expect(response).to have_http_status(:ok)
          expect(json_response[:events]).to be_an(Array)
          expect(json_response[:planner_notes]).to be_an(Array)
          expect(json_response[:items]).to be_an(Array)
        end

        it 'calls services with undated parameter' do
          expect_any_instance_of(Calendar::EventsService).to receive(:new).exactly(3).times.and_call_original
          expect_any_instance_of(Calendar::PlannerNotesService).to receive(:new).once.and_call_original
          expect_any_instance_of(Calendar::ItemsService).to receive(:new).once.and_call_original

          get undated_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params
        end
      end
    end

    context 'when authenticated as account admin' do
      include_context 'with account admin session'
      include_examples 'undated events functionality'
    end

    context 'when authenticated as course admin' do
      include_context 'with course admin session'
      include_examples 'undated events functionality'
    end
  end

  describe 'GET /api/v1/students/:student_id/calendar/all_events' do
    include_context 'with mocked canvas responses'

    shared_examples 'all events functionality' do
      context 'successful request' do
        it 'returns all calendar events for the student' do
          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params

          expect(response).to have_http_status(:ok)
          expect(json_response[:events]).to be_an(Array)
          expect(json_response[:planner_notes]).to be_an(Array)
          expect(json_response[:items]).to be_an(Array)
        end

        it 'returns correct data structure with all event types' do
          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params

          expect(response).to have_http_status(:ok)

          # Verify events structure
          expect(json_response[:events]).to be_an(Array)
          if json_response[:events].any?
            event = json_response[:events].first
            expect(event).to have_key('id')
            expect(event).to have_key('title')
          end

          # Verify planner notes structure
          expect(json_response[:planner_notes]).to be_an(Array)
          if json_response[:planner_notes].any?
            note = json_response[:planner_notes].first
            expect(note).to have_key('id')
            expect(note).to have_key('title')
          end

          # Verify items structure
          expect(json_response[:items]).to be_an(Array)
          if json_response[:items].any?
            item = json_response[:items].first
            expect(item).to have_key('plannable_id')
            expect(item).to have_key('plannable_type')
            expect(item).to have_key('plannable')
          end
        end

        it 'accepts date range parameters' do
          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params.merge({
                                             start_date: '2025-01-01T00:00:00Z',
                                             end_date: '2025-01-31T23:59:59Z'
                                           })

          expect(response).to have_http_status(:ok)
          expect(json_response[:events]).to be_an(Array)
          expect(json_response[:planner_notes]).to be_an(Array)
          expect(json_response[:items]).to be_an(Array)
        end

        it 'accepts context_codes parameter' do
          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params.merge({ context_codes: ["course_#{course.canvas_id}"] })

          expect(response).to have_http_status(:ok)
          expect(json_response[:events]).to be_an(Array)
          expect(json_response[:planner_notes]).to be_an(Array)
          expect(json_response[:items]).to be_an(Array)
        end

        it 'accepts filter parameter' do
          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params.merge({ filter: 'new_activity' })

          expect(response).to have_http_status(:ok)
          expect(json_response[:events]).to be_an(Array)
          expect(json_response[:planner_notes]).to be_an(Array)
          expect(json_response[:items]).to be_an(Array)
        end

        it 'accepts include parameter' do
          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params.merge({ include: %w[web_conference series_head] })

          expect(response).to have_http_status(:ok)
          expect(json_response[:events]).to be_an(Array)
          expect(json_response[:planner_notes]).to be_an(Array)
          expect(json_response[:items]).to be_an(Array)
        end

        it 'calls EventsService 3 times with different event types' do
          expect(Calendar::EventsService).to receive(:new).with(student.canvas_id, hash_including(type: nil)).and_call_original
          expect(Calendar::EventsService).to receive(:new).with(student.canvas_id, hash_including(type: 'assignment')).and_call_original
          expect(Calendar::EventsService).to receive(:new).with(student.canvas_id, hash_including(type: 'sub_assignment')).and_call_original
          expect(Calendar::PlannerNotesService).to receive(:new).with(student.canvas_id, anything).and_call_original
          expect(Calendar::ItemsService).to receive(:new).with(student.canvas_id, anything).and_call_original

          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params
        end

        it 'passes calendar parameters to all services' do
          calendar_params = {
            start_date: '2025-01-01T00:00:00Z',
            end_date: '2025-01-31T23:59:59Z',
            context_codes: ["course_#{course.canvas_id}"],
            filter: 'new_activity'
          }

          expect(Calendar::EventsService).to receive(:new).exactly(3).times do |user_id, params|
            expect(user_id).to eq(student.canvas_id)
            expect(params).to include(
              start_date: calendar_params[:start_date],
              end_date: calendar_params[:end_date],
              context_codes: calendar_params[:context_codes],
              filter: calendar_params[:filter]
            )
            double('service', call: [])
          end

          expect(Calendar::PlannerNotesService).to receive(:new) do |user_id, params|
            expect(user_id).to eq(student.canvas_id)
            expect(params).to include(
              start_date: calendar_params[:start_date],
              end_date: calendar_params[:end_date],
              context_codes: calendar_params[:context_codes],
              filter: calendar_params[:filter]
            )
            double('service', call: [])
          end

          expect(Calendar::ItemsService).to receive(:new) do |user_id, params|
            expect(user_id).to eq(student.canvas_id)
            expect(params).to include(
              start_date: calendar_params[:start_date],
              end_date: calendar_params[:end_date],
              context_codes: calendar_params[:context_codes],
              filter: calendar_params[:filter]
            )
            double('service', call: [])
          end

          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params.merge(calendar_params)
        end
      end

      context 'error handling' do
        it 'returns 404 when student is not found' do
          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: 99_999),
              params: default_params

          expect(response).to have_http_status(:not_found)
          expect(json_response[:error]).to eq('Student not found')
        end

        it 'handles service errors gracefully' do
          allow_any_instance_of(Calendar::EventsService).to receive(:call).and_raise(StandardError.new('Canvas API Error'))

          expect do
            get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
                params: default_params
          end.to raise_error(StandardError, 'Canvas API Error')
        end
      end

      context 'parameter validation' do
        it 'filters out invalid parameters' do
          expect(Calendar::EventsService).to receive(:new) do |_user_id, params|
            expect(params).not_to include(:invalid_param)
            double('service', call: [])
          end.exactly(3).times

          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params.merge({ invalid_param: 'should_be_filtered' })
        end

        it 'accepts valid date formats' do
          valid_dates = {
            start_date: '2025-01-01T00:00:00Z',
            end_date: '2025-12-31T23:59:59Z'
          }

          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params.merge(valid_dates)

          expect(response).to have_http_status(:ok)
        end
      end
    end

    context 'when authenticated as account admin' do
      include_context 'with account admin session'
      include_examples 'all events functionality'

      it 'can access all events for students from any course in the account' do
        other_course_student = other_students.first
        get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: other_course_student.canvas_id),
            params: default_params

        expect(response).to have_http_status(:ok)
        expect(json_response[:events]).to be_an(Array)
        expect(json_response[:planner_notes]).to be_an(Array)
        expect(json_response[:items]).to be_an(Array)
      end
    end

    context 'when authenticated as course admin' do
      include_context 'with course admin session'
      include_examples 'all events functionality'

      it 'can access all events for students in their course' do
        get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
            params: default_params

        expect(response).to have_http_status(:ok)
        expect(json_response[:events]).to be_an(Array)
        expect(json_response[:planner_notes]).to be_an(Array)
        expect(json_response[:items]).to be_an(Array)
      end

      it 'cannot access all events for students from other courses' do
        other_course_student = other_students.first
        get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: other_course_student.canvas_id),
            params: default_params

        expect(response).to have_http_status(:not_found)
        expect(json_response[:error]).to eq('Student not found')
      end
    end

    context 'authentication and authorization' do
      it 'requires authentication' do
        get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id)

        expect(response).to have_http_status(:unauthorized)
      end

      it 'requires valid session' do
        get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
            params: { session_key: 'invalid_session_key' }

        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'when authenticated as account admin' do
      include_context 'with account admin session'

      it 'requires proper authorization' do
        expect_any_instance_of(Api::V1::Students::CalendarsController).to receive(:authorize!).with(:read, User).and_call_original

        get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
            params: default_params

        expect(response).to have_http_status(:ok)
      end
    end
  end
end
