# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Students::CalendarsController', type: :request do
  let(:organization)   { current_organization }
  let!(:account)       { create(:account) }
  let!(:course)        { create(:course, account: account) }
  let!(:other_course)  { create(:course, account: account) }

  let!(:account_admin) { create(:admin, account: account).user }
  let!(:course_admin)  { create(:enrollment, :teacher, user: create(:user), course: course).user }

  let!(:admin_session) { create(:session, :admin, user: account_admin, account: account, launch_context: account) }
  let!(:course_admin_session) { create(:session, :teacher, user: course_admin, account: account, launch_context: course) }

  # Students in the main course
  let!(:students) { create_list(:user, 3).each { |student| create(:enrollment, :student, user: student, course: course) } }
  # Students in other course (should only be visible to account admin)
  let!(:other_students) { create_list(:user, 2).each { |student| create(:enrollment, :student, user: student, course: other_course) } }

  let(:student) { students.first }

  shared_context 'with account admin session' do
    let(:current_session) { admin_session }
    let(:default_params) { { session_key: admin_session.session_key } }
  end

  shared_context 'with course admin session' do
    let(:current_session) { course_admin_session }
    let(:default_params) { { session_key: course_admin_session.session_key } }
  end

  shared_context 'with mocked canvas responses' do
    let(:mock_events_response) do
      [
        {
          id: '1',
          title: 'Test Event',
          start_at: '2025-01-15T10:00:00Z',
          end_at: '2025-01-15T11:00:00Z',
          context_code: "user_#{student.canvas_id}"
        }
      ]
    end

    let(:mock_planner_notes_response) do
      [
        {
          id: '1',
          title: 'Test Note',
          todo_date: '2025-01-15T10:00:00Z',
          details: 'Test planner note details'
        }
      ]
    end

    let(:mock_items_response) do
      [
        {
          plannable_id: '1',
          plannable_type: 'assignment',
          plannable: {
            id: '1',
            title: 'Test Assignment',
            due_at: '2025-01-15T23:59:59Z'
          }
        }
      ]
    end

    before do
      # Mock EventsService calls
      allow_any_instance_of(Calendar::EventsService).to receive(:call).and_return(mock_events_response)

      # Mock PlannerNotesService calls
      allow_any_instance_of(Calendar::PlannerNotesService).to receive(:call).and_return(mock_planner_notes_response)

      # Mock ItemsService calls
      allow_any_instance_of(Calendar::ItemsService).to receive(:call).and_return(mock_items_response)
    end
  end

  describe 'GET /api/v1/students/:student_id/calendar/all_events' do
    include_context 'with mocked canvas responses'

    shared_examples 'all events functionality' do
      context 'successful request' do
        it 'returns all calendar events for the student' do
          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params

          expect(response).to have_http_status(:ok)
          expect(json_response[:events]).to be_an(Array)
          expect(json_response[:planner_notes]).to be_an(Array)
          expect(json_response[:items]).to be_an(Array)
        end

        it 'returns correct data structure with all event types' do
          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params

          expect(response).to have_http_status(:ok)

          # Verify events structure
          expect(json_response[:events]).to be_an(Array)
          expect(json_response[:events].first).to have_key(:id)
          expect(json_response[:events].first).to have_key(:title)
          expect(json_response[:events].first).to have_key(:start_at)
          expect(json_response[:events].first).to have_key(:end_at)

          # Verify planner notes structure
          expect(json_response[:planner_notes]).to be_an(Array)
          expect(json_response[:planner_notes].first).to have_key(:id)
          expect(json_response[:planner_notes].first).to have_key(:title)
          expect(json_response[:planner_notes].first).to have_key(:todo_date)

          # Verify items structure
          expect(json_response[:items]).to be_an(Array)
          expect(json_response[:items].first).to have_key(:plannable_id)
          expect(json_response[:items].first).to have_key(:plannable_type)
          expect(json_response[:items].first).to have_key(:plannable)
        end

        it 'accepts date range parameters' do
          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params.merge({
                                             start_date: '2025-01-01T00:00:00Z',
                                             end_date: '2025-01-31T23:59:59Z'
                                           })

          expect(response).to have_http_status(:ok)
          expect(json_response[:events]).to be_an(Array)
          expect(json_response[:planner_notes]).to be_an(Array)
          expect(json_response[:items]).to be_an(Array)
        end

        it 'accepts context_codes parameter' do
          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params.merge({ context_codes: ["course_#{course.canvas_id}"] })

          expect(response).to have_http_status(:ok)
          expect(json_response[:events]).to be_an(Array)
          expect(json_response[:planner_notes]).to be_an(Array)
          expect(json_response[:items]).to be_an(Array)
        end

        it 'accepts filter parameter' do
          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params.merge({ filter: 'new_activity' })

          expect(response).to have_http_status(:ok)
          expect(json_response[:events]).to be_an(Array)
          expect(json_response[:planner_notes]).to be_an(Array)
          expect(json_response[:items]).to be_an(Array)
        end

        it 'accepts include parameter' do
          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params.merge({ include: %w[web_conference series_head] })

          expect(response).to have_http_status(:ok)
          expect(json_response[:events]).to be_an(Array)
          expect(json_response[:planner_notes]).to be_an(Array)
          expect(json_response[:items]).to be_an(Array)
        end

        it 'calls EventsService 3 times with different event types' do
          expect(Calendar::EventsService).to receive(:new).with(student.canvas_id, hash_including(type: nil)).and_call_original
          expect(Calendar::EventsService).to receive(:new).with(student.canvas_id, hash_including(type: 'assignment')).and_call_original
          expect(Calendar::EventsService).to receive(:new).with(student.canvas_id, hash_including(type: 'sub_assignment')).and_call_original
          expect(Calendar::PlannerNotesService).to receive(:new).with(student.canvas_id, anything).and_call_original
          expect(Calendar::ItemsService).to receive(:new).with(student.canvas_id, anything).and_call_original

          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params
        end

        it 'passes calendar parameters to all services' do
          calendar_params = {
            start_date: '2025-01-01T00:00:00Z',
            end_date: '2025-01-31T23:59:59Z',
            context_codes: ["course_#{course.canvas_id}"],
            filter: 'new_activity'
          }

          expect(Calendar::EventsService).to receive(:new).exactly(3).times do |user_id, params|
            expect(user_id).to eq(student.canvas_id)
            expect(params).to include(
              start_date: calendar_params[:start_date],
              end_date: calendar_params[:end_date],
              context_codes: calendar_params[:context_codes],
              filter: calendar_params[:filter]
            )
            double('service', call: mock_events_response)
          end

          expect(Calendar::PlannerNotesService).to receive(:new) do |user_id, params|
            expect(user_id).to eq(student.canvas_id)
            expect(params).to include(
              start_date: calendar_params[:start_date],
              end_date: calendar_params[:end_date],
              context_codes: calendar_params[:context_codes],
              filter: calendar_params[:filter]
            )
            double('service', call: mock_planner_notes_response)
          end

          expect(Calendar::ItemsService).to receive(:new) do |user_id, params|
            expect(user_id).to eq(student.canvas_id)
            expect(params).to include(
              start_date: calendar_params[:start_date],
              end_date: calendar_params[:end_date],
              context_codes: calendar_params[:context_codes],
              filter: calendar_params[:filter]
            )
            double('service', call: mock_items_response)
          end

          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params.merge(calendar_params)
        end
      end

      context 'error handling' do
        it 'returns 404 when student is not found' do
          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: 99_999),
              params: default_params

          expect(response).to have_http_status(:not_found)
          expect(json_response[:error]).to eq('Student not found')
        end

        it 'handles service errors gracefully' do
          allow_any_instance_of(Calendar::EventsService).to receive(:call).and_raise(StandardError.new('Canvas API Error'))

          expect do
            get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
                params: default_params
          end.to raise_error(StandardError, 'Canvas API Error')
        end
      end

      context 'parameter validation' do
        it 'filters out invalid parameters' do
          expect(Calendar::EventsService).to receive(:new).exactly(3).times do |_user_id, params|
            expect(params).not_to include(:invalid_param)
            double('service', call: mock_events_response)
          end

          expect(Calendar::PlannerNotesService).to receive(:new) do |_user_id, params|
            expect(params).not_to include(:invalid_param)
            double('service', call: mock_planner_notes_response)
          end

          expect(Calendar::ItemsService).to receive(:new) do |_user_id, params|
            expect(params).not_to include(:invalid_param)
            double('service', call: mock_items_response)
          end

          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params.merge({ invalid_param: 'should_be_filtered' })
        end

        it 'accepts valid date formats' do
          valid_dates = {
            start_date: '2025-01-01T00:00:00Z',
            end_date: '2025-12-31T23:59:59Z'
          }

          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params.merge(valid_dates)

          expect(response).to have_http_status(:ok)
        end

        it 'accepts undated parameter' do
          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params.merge({ undated: true })

          expect(response).to have_http_status(:ok)
        end

        it 'accepts type parameter' do
          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params.merge({ type: 'assignment' })

          expect(response).to have_http_status(:ok)
        end
      end
    end

    context 'when authenticated as account admin' do
      include_context 'with account admin session'
      include_examples 'all events functionality'

      it 'can access all events for students from any course in the account' do
        other_course_student = other_students.first
        get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: other_course_student.canvas_id),
            params: default_params

        expect(response).to have_http_status(:ok)
        expect(json_response[:events]).to be_an(Array)
        expect(json_response[:planner_notes]).to be_an(Array)
        expect(json_response[:items]).to be_an(Array)
      end
    end

    context 'when authenticated as course admin' do
      include_context 'with course admin session'
      include_examples 'all events functionality'

      it 'can access all events for students in their course' do
        get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
            params: default_params

        expect(response).to have_http_status(:ok)
        expect(json_response[:events]).to be_an(Array)
        expect(json_response[:planner_notes]).to be_an(Array)
        expect(json_response[:items]).to be_an(Array)
      end

      it 'cannot access all events for students from other courses' do
        other_course_student = other_students.first
        get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: other_course_student.canvas_id),
            params: default_params

        expect(response).to have_http_status(:not_found)
        expect(json_response[:error]).to eq('Student not found')
      end
    end

    context 'authentication and authorization' do
      it 'requires authentication' do
        get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id)

        expect(response).to have_http_status(:unauthorized)
      end

      it 'requires valid session' do
        get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
            params: { session_key: 'invalid_session_key' }

        expect(response).to have_http_status(:unauthorized)
      end

      context 'when authenticated as account admin' do
        include_context 'with account admin session'

        it 'requires proper authorization' do
          expect_any_instance_of(Api::V1::Students::CalendarsController).to receive(:authorize!).with(:read, User).and_call_original

          get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
              params: default_params

          expect(response).to have_http_status(:ok)
        end
      end
    end

    context 'response format validation' do
      include_context 'with account admin session'

      it 'returns JSON response with correct content type' do
        get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
            params: default_params

        expect(response).to have_http_status(:ok)
        expect(response.content_type).to include('application/json')
      end

      it 'returns consistent response structure' do
        get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
            params: default_params

        expect(response).to have_http_status(:ok)
        expect(json_response).to include(:events, :planner_notes, :items)
        expect(json_response.keys.size).to eq(3)
      end

      it 'handles empty responses gracefully' do
        allow_any_instance_of(Calendar::EventsService).to receive(:call).and_return([])
        allow_any_instance_of(Calendar::PlannerNotesService).to receive(:call).and_return([])
        allow_any_instance_of(Calendar::ItemsService).to receive(:call).and_return([])

        get all_events_api_v1_student_calendar_path(organization_id: organization.id, student_id: student.canvas_id),
            params: default_params

        expect(response).to have_http_status(:ok)
        expect(json_response[:events]).to eq([])
        expect(json_response[:planner_notes]).to eq([])
        expect(json_response[:items]).to eq([])
      end
    end
  end
end
