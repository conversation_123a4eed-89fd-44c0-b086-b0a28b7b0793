# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::StudentsController', type: :request do
  let(:organization)   { current_organization }
  let!(:account)       { create(:account) }
  let!(:course)        { create(:course, account: account) }
  let!(:other_course)  { create(:course, account: account) }

  let!(:account_admin) { create(:admin, account: account).user }
  let!(:course_admin)  { create(:enrollment, :teacher, user: create(:user), course: course).user }

  let!(:admin_session) { create(:session, :admin, user: account_admin, account: account, launch_context: account) }
  let!(:course_admin_session) { create(:session, :teacher, user: course_admin, account: account, launch_context: course) }

  # Students in the main course
  let!(:students) { create_list(:user, 3).each { |student| create(:enrollment, :student, user: student, course: course) } }
  # Students in other course (should only be visible to account admin)
  let!(:other_students) { create_list(:user, 2).each { |student| create(:enrollment, :student, user: student, course: other_course) } }

  shared_context 'with account admin session' do
    let(:current_session) { admin_session }
    let(:default_params) { { session_key: admin_session.session_key } }
    let(:expected_student_count) { 5 } # All students from both courses
  end

  shared_context 'with course admin session' do
    let(:current_session) { course_admin_session }
    let(:default_params) { { session_key: course_admin_session.session_key } }
    let(:expected_student_count) { 3 } # Only students from the specific course
  end

  shared_context 'with searchable student' do
    let!(:searchable_student) do
      students.first.tap do |student|
        student.update!(
          first_name: 'UniqueName',
          last_name: 'UniqueLastName',
          sortable_name: 'UniqueLastName, UniqueName'
        )
      end
    end
  end

  describe 'GET /api/v1/students' do
    shared_examples 'student listing functionality' do
      context 'basic functionality' do
        it 'returns students sorted ascending by sortable_name by default' do
          get api_v1_students_path(organization_id: organization.id), params: default_params

          expect(response).to have_http_status(:ok)
          expect(json_response[:users].size).to eq(expected_student_count)
        end
      end

      context 'with search parameter' do
        include_context 'with searchable student'

        it 'returns filtered students when search term is 3+ characters' do
          get api_v1_students_path(organization_id: organization.id), params: default_params.merge({ search: searchable_student.first_name })

          expect(response).to have_http_status(:ok)
          users = json_response[:users]
          expect(users.size).to eq(1)
          expect(users.first[:sortable_name]).to eq(searchable_student.sortable_name)
        end

        it 'returns all students when search term is less than 3 characters' do
          get api_v1_students_path(organization_id: organization.id), params: default_params.merge({ search: 'ab' })

          expect(response).to have_http_status(:ok)
          expect(json_response[:users].size).to eq(expected_student_count)
        end

        it 'returns all students when no search parameter is provided' do
          get api_v1_students_path(organization_id: organization.id), params: default_params

          expect(response).to have_http_status(:ok)
          expect(json_response[:users].size).to eq(expected_student_count)
        end
      end

      context 'with sorting parameters' do
        it 'sorts students by sortable_name in ascending order by default' do
          get api_v1_students_path(organization_id: organization.id), params: default_params

          expect(response).to have_http_status(:ok)
          users = json_response[:users]
          expect(users).to eq(users.sort_by { |u| u[:sortable_name] })
        end

        it 'sorts students by sortable_name in descending order when specified' do
          get api_v1_students_path(organization_id: organization.id), params: default_params.merge({ sort_order: 'DESC' })

          expect(response).to have_http_status(:ok)
          users = json_response[:users]
          expect(users).to eq(users.sort_by { |u| u[:sortable_name] }.reverse)
        end
      end
    end

    context 'when authenticated as account admin' do
      include_context 'with account admin session'
      include_examples 'student listing functionality'

      it 'can see students from all courses in the account' do
        get api_v1_students_path(organization_id: organization.id), params: default_params

        expect(response).to have_http_status(:ok)
        returned_canvas_ids = json_response[:users].map { |u| u[:canvas_id] }
        all_student_canvas_ids = (students + other_students).map(&:canvas_id)
        expect(returned_canvas_ids).to match_array(all_student_canvas_ids)
      end
    end

    context 'when authenticated as course admin' do
      include_context 'with course admin session'
      include_examples 'student listing functionality'

      it 'can only see students from their specific course' do
        get api_v1_students_path(organization_id: organization.id), params: default_params

        expect(response).to have_http_status(:ok)
        returned_canvas_ids = json_response[:users].map { |u| u[:canvas_id] }
        course_student_canvas_ids = students.map(&:canvas_id)
        other_course_student_canvas_ids = other_students.map(&:canvas_id)

        expect(returned_canvas_ids).to match_array(course_student_canvas_ids)
        expect(returned_canvas_ids).not_to include(*other_course_student_canvas_ids)
      end
    end
  end

  describe 'POST /api/v1/students/:id/create_observer_link' do
    let(:student) { students.first }

    shared_examples 'observer link creation functionality' do
      context 'successful creation' do
        it 'creates an observer link successfully' do
          post create_observer_link_api_v1_student_path(organization_id: organization.id, id: student.canvas_id), params: default_params

          expect(response).to have_http_status(:created)
          expect(json_response[:message]).to eq('Observer link created successfully')
          expect(json_response[:data]).to include(
            :organization_id,
            :id,
            :observer_user_id,
            :observed_student,
            :created_at,
            :expires_at
          )
        end
      end

      context 'error handling' do
        it 'returns error when student is not found' do
          post create_observer_link_api_v1_student_path(organization_id: organization.id, id: 99_999), params: default_params

          expect(response).to have_http_status(:not_found)
          expect(json_response[:error]).to eq('Student not found')
        end

        context 'when observer already has an active link' do
          before do
            create(:student_observer_link, observed_student: student, observer_user: current_user)
          end

          it 'returns error message about existing active link' do
            post create_observer_link_api_v1_student_path(organization_id: organization.id, id: student.canvas_id), params: default_params

            expect(response).to have_http_status(:unprocessable_entity)
            expect(json_response[:error]).to eq('You already have an active observer link. Please end the current link before creating a new one.')
          end
        end
      end
    end

    context 'when authenticated as account admin' do
      include_context 'with account admin session'
      let(:current_user) { account_admin }
      include_examples 'observer link creation functionality'

      it 'can create observer links for students from any course in the account' do
        other_course_student = other_students.first
        post create_observer_link_api_v1_student_path(organization_id: organization.id, id: other_course_student.canvas_id), params: default_params

        expect(response).to have_http_status(:created)
        expect(json_response[:message]).to eq('Observer link created successfully')
      end
    end

    context 'when authenticated as course admin' do
      include_context 'with course admin session'
      let(:current_user) { course_admin }
      include_examples 'observer link creation functionality'

      it 'can create observer links for students in their course' do
        post create_observer_link_api_v1_student_path(organization_id: organization.id, id: student.canvas_id), params: default_params

        expect(response).to have_http_status(:created)
        expect(json_response[:message]).to eq('Observer link created successfully')
      end

      it 'cannot create observer links for students from other courses' do
        other_course_student = other_students.first
        post create_observer_link_api_v1_student_path(organization_id: organization.id, id: other_course_student.canvas_id), params: default_params

        expect(response).to have_http_status(:not_found)
        expect(json_response[:error]).to eq('Student not found')
      end

      it 'cannot create observer links for non-existent students' do
        post create_observer_link_api_v1_student_path(organization_id: organization.id, id: 99_999), params: default_params

        expect(response).to have_http_status(:not_found)
        expect(json_response[:error]).to eq('Student not found')
      end
    end
  end

  describe 'GET /api/v1/students/:id/calendars' do
    let(:student) { students.first }

    before do
      # Mock the Canvas API response for user colors using Bearcat::Client
      allow_any_instance_of(Bearcat::Client).to receive(:get)
        .with("api/v1/users/#{student.canvas_id}/colors")
        .and_return({
                      custom_colors: {
                        "user_#{student.canvas_id}" => '#FF0000',
                        "course_#{course.canvas_id}" => '#00FF00'
                      }
                    })
    end

    shared_examples 'calendars functionality' do
      context 'successful request' do
        it 'returns calendar data for the student' do
          get calendars_api_v1_student_path(organization_id: organization.id, id: student.canvas_id), params: default_params

          expect(response).to have_http_status(:ok)
          calendar_data = JSON.parse(response.body)
          expect(calendar_data).to be_an(Array)

          # Should include user context
          user_context = calendar_data.find { |item| item['type'] == 'user' }
          expect(user_context).to be_present
          expect(user_context['name']).to eq(student.name)
          expect(user_context['color']).to eq('#FF0000')

          # Should include course context from current shard
          course_context = calendar_data.find { |item| item['type'] == 'course' && item['id'] == course.canvas_id.to_s }
          expect(course_context).to be_present
          expect(course_context['name']).to eq(course.name)
          expect(course_context['color']).to eq('#00FF00')
        end
      end

      context 'error handling' do
        it 'returns 404 when student is not found' do
          get calendars_api_v1_student_path(organization_id: organization.id, id: 99_999), params: default_params

          expect(response).to have_http_status(:not_found)
          expect(json_response[:error]).to eq('Student not found')
        end
      end
    end

    context 'when authenticated as account admin' do
      include_context 'with account admin session'
      include_examples 'calendars functionality'
    end

    context 'when authenticated as course admin' do
      include_context 'with course admin session'
      include_examples 'calendars functionality'
    end
  end
end
