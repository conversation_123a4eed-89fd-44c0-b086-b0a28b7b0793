# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Calendar::ItemsService do
  let(:user_id) { 123 }
  let(:mock_client) { double('canvas_sync_client') }
  let(:bearcat_api_array) { double('bearcat_api_array') }

  let(:params) do
    {
      filter: 'ungraded_todo_items',
      start_date: '2025-08-31T18:30:00.000Z',
      end_date: '2025-10-05T18:30:00.000Z',
      context_codes: %w[course_1]
    }
  end

  let(:mock_response) do
    [
      {
        context_type: 'Course',
        course_id: '1',
        plannable_id: '3',
        plannable_type: 'announcement',
        new_activity: true,
        submissions: false,
        plannable_date: '2025-09-04T07:12:14Z',
        plannable: {
          id: '3',
          title: 'Shard 1 Announcement',
          unread_count: 0,
          read_state: 'unread',
          created_at: '2025-09-04T07:12:14Z',
          updated_at: '2025-09-04T07:12:31Z'
        },
        html_url: '/courses/1/discussion_topics/3',
        context_name: 'Shard1 Course'
      },
      {
        context_type: 'Course',
        course_id: '1',
        plannable_id: '4',
        plannable_type: 'announcement',
        new_activity: true,
        submissions: false,
        plannable_date: '2025-09-04T07:13:34Z',
        plannable: {
          id: '4',
          title: 'Shard 2 Announcement',
          unread_count: 0,
          read_state: 'unread',
          created_at: '2025-09-04T07:13:34Z',
          updated_at: '2025-09-04T07:13:34Z'
        },
        html_url: '/courses/1/discussion_topics/4',
        context_name: 'Shard1 Course'
      }
    ]
  end

  describe '#call' do
    context 'with basic parameters' do
      let(:service) { described_class.new(user_id, params) }

      it 'calls the Canvas API with correct path and basic parameters' do
        stub_request(:get, 'https://example.com/api/v1/planner/items')
          .with(
            query: hash_including(
              'filter' => 'ungraded_todo_items',
              'start_date' => '2025-08-31T18:30:00.000Z',
              'end_date' => '2025-10-05T18:30:00.000Z',
              'context_codes' => ['course_1']
            )
          )
          .to_return(
            status: 200,
            body: mock_response.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )

        allow(Date).to receive(:today).and_return(Date.new(2025, 1, 15))

        expect(service.call).to eq(JSON.parse(mock_response.to_json))
      end
    end
  end
end
